import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Play, RefreshCw, Copy, Download } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface CodePreviewProps {
  code: string;
  language: string;
  filename: string;
  onRun?: () => void;
}

const CodePreview: React.FC<CodePreviewProps> = ({
  code,
  language,
  filename,
  onRun
}) => {
  const { toast } = useToast();
  const [output, setOutput] = useState<string>('');
  const [isRunning, setIsRunning] = useState<boolean>(false);
  const [sessionId, setSessionId] = useState<string | null>(null);

  // Get session ID from terminal server
  useEffect(() => {
    const getSessionId = async () => {
      try {
        const response = await fetch('http://localhost:3002/api/sessions');
        const sessions = await response.json();

        if (sessions && sessions.length > 0) {
          setSessionId(sessions[0].id);
        }
      } catch (error) {
        console.error('Error getting session ID:', error);
      }
    };

    getSessionId();
  }, []);

  // Run code and get output
  const runCode = async () => {
    if (!sessionId) {
      toast({
        title: 'Error',
        description: 'Terminal session not found. Please make sure the terminal server is running.',
        variant: 'destructive'
      });
      return;
    }

    setIsRunning(true);
    setOutput('Running code...');

    try {
      // First, save the code to a file
      const saveResponse = await fetch('http://localhost:3002/api/files', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          filename,
          content: code,
          sessionId
        })
      });

      if (!saveResponse.ok) {
        throw new Error('Failed to save file');
      }

      // Then, run the code
      const runResponse = await fetch('http://localhost:3002/api/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          filename,
          sessionId
        })
      });

      if (!runResponse.ok) {
        throw new Error('Failed to run code');
      }

      const result = await runResponse.json();
      setOutput(result.output);

      toast({
        title: 'Code Executed',
        description: `${filename} executed successfully`,
      });
    } catch (error) {
      console.error('Error running code:', error);
      setOutput(`Error: ${error instanceof Error ? error.message : String(error)}`);

      toast({
        title: 'Error',
        description: 'Failed to run code. See output for details.',
        variant: 'destructive'
      });
    } finally {
      setIsRunning(false);
    }
  };

  // Copy output to clipboard
  const copyOutput = () => {
    navigator.clipboard.writeText(output);

    toast({
      title: 'Copied',
      description: 'Output copied to clipboard',
    });
  };

  // Download output as file
  const downloadOutput = () => {
    const blob = new Blob([output], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${filename.split('.')[0]}-output.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: 'Downloaded',
      description: 'Output downloaded as file',
    });
  };

  // Auto-run code when it changes
  useEffect(() => {
    if (code && sessionId && !isRunning) {
      const timer = setTimeout(() => {
        runCode();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [code, sessionId]);

  // Run code when component mounts
  useEffect(() => {
    if (sessionId) {
      runCode();
    }
  }, [sessionId]);

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium flex items-center justify-between">
          <span>Output Preview: {filename}</span>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={runCode}
              disabled={isRunning || !sessionId}
              className="h-7 px-2"
            >
              {isRunning ? <RefreshCw className="h-3.5 w-3.5 animate-spin" /> : <Play className="h-3.5 w-3.5" />}
              <span className="ml-1">{isRunning ? 'Running...' : 'Run'}</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={copyOutput}
              disabled={!output}
              className="h-7 w-7 p-0"
            >
              <Copy className="h-3.5 w-3.5" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={downloadOutput}
              disabled={!output}
              className="h-7 w-7 p-0"
            >
              <Download className="h-3.5 w-3.5" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="flex-1 overflow-auto p-2">
        <pre className="text-xs font-mono bg-muted/50 p-3 rounded-md overflow-auto h-full whitespace-pre-wrap">
          {output || 'Code output will appear here...'}
        </pre>
      </CardContent>
    </Card>
  );
};

export default CodePreview;
