
import { FileNode } from "@/components/FileExplorer";

export interface OllamaResponse {
  response: string;
  actions: Array<{
    title: string;
    description: string;
    status: 'pending' | 'complete' | 'error';
    output?: string;
  }>;
}

interface ProjectStats {
  totalFiles: number;
  totalLines: number;
  fileTypes: { name: string; count: number }[];
  issuesCount: number;
}

interface OllamaAPIResponse {
  model: string;
  created_at: string;
  message: {
    role: string;
    content: string;
  };
  done: boolean;
  context?: number[];
  total_duration?: number;
  load_duration?: number;
  prompt_eval_count?: number;
  prompt_eval_duration?: number;
  eval_count?: number;
  eval_duration?: number;
}

// Get the Ollama API URL and model from localStorage or use defaults
const getOllamaConfig = () => {
  // Try to get the active server from localStorage
  const savedServers = localStorage.getItem('mcp-servers');
  if (savedServers) {
    const servers = JSON.parse(savedServers);
    // Find the first online server, or just use the first one
    const activeServer = servers.find((s: any) => s.isOnline) || servers[0];
    if (activeServer) {
      // Use proxy for local development to avoid CORS issues
      const isLocalhost = activeServer.host === 'localhost' || activeServer.host === '127.0.0.1';
      const baseUrl = isLocalhost 
        ? `/ollama-api` // Use proxy path
        : `http://${activeServer.host}:${activeServer.port}`;
      
      console.log(`Using Ollama API at: ${baseUrl}`);
      
      return {
        apiUrl: `${baseUrl}/api/generate`,
        model: 'qwen2.5-coder:7b' // Varsayılan model
      };
    }
  }
  
  // Default fallback using proxy
  console.log('Using default Ollama API configuration');
  return {
    apiUrl: '/ollama-api/api/generate', // Use proxy path
    model: 'qwen2.5-coder:7b'
  };
};

// Kod bloklarını dosyalara ayıran ve kaydeden fonksiyon
function extractCodeBlocks(response: string): Array<{filePath: string, code: string}> {
  const codeBlockRegex = /```(?:(\w+)\s+)?(?:file:([^\n]+))?\n([\s\S]*?)```/g;
  const codeBlocks: Array<{filePath: string, code: string}> = [];
  
  let match;
  while ((match = codeBlockRegex.exec(response)) !== null) {
    const language = match[1] || '';
    const filePath = match[2] || '';
    const code = match[3] || '';
    
    if (filePath) {
      codeBlocks.push({
        filePath: filePath.trim(),
        code: code
      });
    }
  }
  
  return codeBlocks;
}

// Dosya oluşturma veya güncelleme fonksiyonu
export async function saveCodeToFiles(response: string, files: FileNode[]): Promise<Array<{filePath: string, success: boolean, message: string}>> {
  const codeBlocks = extractCodeBlocks(response);
  const results: Array<{filePath: string, success: boolean, message: string}> = [];
  
  if (codeBlocks.length === 0) {
    return [{
      filePath: '',
      success: false,
      message: 'Yanıtta dosya yolu belirtilen kod bloğu bulunamadı.'
    }];
  }
  
  for (const block of codeBlocks) {
    try {
      // Dosya yolunu parçalara ayır
      const pathParts = block.filePath.split('/');
      const fileName = pathParts.pop() || '';
      const folderPath = pathParts.join('/');
      
      // Dosya ID'si oluştur
      const fileId = `file-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
      
      // Dosya uzantısını belirle
      const fileExtension = fileName.split('.').pop() || '';
      
      // Dosya dili belirle
      let language = 'plaintext';
      switch (fileExtension) {
        case 'js': language = 'javascript'; break;
        case 'ts': language = 'typescript'; break;
        case 'jsx': language = 'javascript'; break;
        case 'tsx': language = 'typescript'; break;
        case 'html': language = 'html'; break;
        case 'css': language = 'css'; break;
        case 'json': language = 'json'; break;
        case 'py': language = 'python'; break;
        case 'md': language = 'markdown'; break;
        default: language = 'plaintext';
      }
      
      // Mevcut dosyayı bul veya yeni oluştur
      let existingFile = findFileByPath(files, block.filePath);
      
      if (existingFile) {
        // Mevcut dosyayı güncelle
        existingFile.content = block.code;
        results.push({
          filePath: block.filePath,
          success: true,
          message: `Dosya güncellendi: ${block.filePath}`
        });
      } else {
        // Yeni dosya oluştur
        const newFile: FileNode = {
          id: fileId,
          name: fileName,
          type: 'file',
          language,
          content: block.code
        };
        
        // Klasör yapısını oluştur ve dosyayı ekle
        if (folderPath) {
          let currentLevel = files;
          const folders = folderPath.split('/');
          
          for (let i = 0; i < folders.length; i++) {
            const folderName = folders[i];
            if (!folderName) continue;
            
            // Klasörü bul veya oluştur
            let folder = currentLevel.find(node => 
              node.type === 'folder' && node.name === folderName
            ) as FileNode | undefined;
            
            if (!folder) {
              // Klasör yoksa oluştur
              folder = {
                id: `folder-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
                name: folderName,
                type: 'folder',
                children: []
              };
              currentLevel.push(folder);
            }
            
            if (!folder.children) {
              folder.children = [];
            }
            
            currentLevel = folder.children;
          }
          
          // Dosyayı en son klasöre ekle
          currentLevel.push(newFile);
        } else {
          // Kök dizine ekle
          files.push(newFile);
        }
        
        results.push({
          filePath: block.filePath,
          success: true,
          message: `Yeni dosya oluşturuldu: ${block.filePath}`
        });
      }
    } catch (error) {
      results.push({
        filePath: block.filePath,
        success: false,
        message: `Hata: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`
      });
    }
  }
  
  // Dosya değişikliklerini localStorage'a kaydet
  saveFilesToLocalStorage(files);
  
  return results;
}

// Dosyaları localStorage'a kaydetme fonksiyonu
function saveFilesToLocalStorage(files: FileNode[]) {
  try {
    localStorage.setItem('project-files', JSON.stringify(files));
  } catch (error) {
    console.error('Dosyalar localStorage\'a kaydedilemedi:', error);
  }
}

// Dosya yoluna göre dosya bulma fonksiyonu
function findFileByPath(files: FileNode[], path: string): FileNode | null {
  const pathParts = path.split('/');
  const fileName = pathParts.pop() || '';
  
  let currentLevel = files;
  
  // Klasör yapısında ilerle
  for (const part of pathParts) {
    if (!part) continue;
    
    const folder = currentLevel.find(node => 
      node.type === 'folder' && node.name === part
    );
    
    if (!folder || !folder.children) {
      return null;
    }
    
    currentLevel = folder.children;
  }
  
  // Dosyayı bul
  const file = currentLevel.find(node => 
    node.type === 'file' && node.name === fileName
  );
  
  return file || null;
}

export async function processPrompt(prompt: string, files: FileNode[]): Promise<OllamaResponse> {
  console.log('Processing prompt:', prompt);
  
  try {
    const { apiUrl, model } = getOllamaConfig();
    
    // Prepare project context from files
    const fileContext = prepareFileContext(files);
    
    // Create a more conversational prompt that encourages the model to respond directly
    const fullPrompt = `
You are an intelligent AI assistant for code development. You help users with their coding tasks, answer questions, and provide assistance with software development.

Current Project Context:
${fileContext}

User Request: ${prompt}

Please respond directly to the user's request in a helpful and conversational manner. If the user asks you to perform a specific task like generating code, analyzing code, or explaining a concept, focus on that task.

If you're generating or suggesting code, make sure it's practical, well-structured, and follows best practices.

IMPORTANT: When providing code that should be saved to a file, use the following format:
\`\`\`language file:path/to/file.ext
// Your code here
\`\`\`

For example:
\`\`\`javascript file:src/utils/helper.js
function formatDate(date) {
  return new Date(date).toLocaleDateString();
}
\`\`\`

This will allow the system to automatically save the code to the specified file.
`;

    // For development, we can use a proxy server or a CORS browser extension
    // In production, the backend should be configured to handle CORS properly
    
    try {
      console.log(`Sending request to Ollama API: ${apiUrl}`);
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: model,
          prompt: fullPrompt,
          stream: false,
          options: {
            temperature: 0.7,
            top_p: 0.9,
            top_k: 40,
            num_predict: 2048
          }
        }),
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Ollama API response:', data);
        
        // Extract content based on response format
        let content = '';
        if (data.message && typeof data.message.content === 'string') {
          content = data.message.content;
        } else if (typeof data.response === 'string') {
          content = data.response;
        } else if (typeof data.content === 'string') {
          content = data.content;
        } else {
          content = JSON.stringify(data);
        }
        
        // Kod bloklarını dosyalara kaydet
        const saveResults = await saveCodeToFiles(content, files);
        
        // Dosya kaydetme sonuçlarını aksiyonlara dönüştür
        const fileActions = saveResults.map(result => ({
          title: result.success ? 'Dosya Kaydedildi' : 'Dosya Kaydedilemedi',
          description: result.message,
          status: result.success ? 'complete' as const : 'error' as const
        }));
        
        // Process the response to extract any other actions
        const otherActions = extractActionsFromResponse(content);
        
        // Format the response for better readability
        const formattedResponse = formatResponse(content);
        
        return { 
          response: formattedResponse, 
          actions: [...fileActions, ...otherActions]
        };
      } else {
        console.error(`API error: ${response.status} ${response.statusText}`);
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }
    } catch (directError) {
      console.warn('API request failed:', directError);
      throw directError;
    }
  } catch (error) {
    console.error('Error calling Ollama API:', error);
    const { model } = getOllamaConfig();
    return {
      response: `Üzgünüm, isteğinizi işlerken bir hata oluştu. Lütfen Ollama'nın çalıştığından ve ${model} modelinin yüklü olduğundan emin olun. Hata detayı: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`,
      actions: [{
        title: 'API Hatası',
        description: 'Ollama API\'sine bağlanılamadı',
        status: 'error',
        output: error instanceof Error ? error.message : 'Bilinmeyen hata'
      }]
    };
  }
}

// Helper function to format the response for better readability
function formatResponse(response: string): string {
  // Replace markdown code blocks with HTML for better display
  let formatted = response.replace(/```(\w*)([\s\S]*?)```/g, '<pre><code class="language-$1">$2</code></pre>');
  
  // Convert markdown headers
  formatted = formatted.replace(/^# (.*$)/gm, '<h1>$1</h1>');
  formatted = formatted.replace(/^## (.*$)/gm, '<h2>$1</h2>');
  formatted = formatted.replace(/^### (.*$)/gm, '<h3>$1</h3>');
  
  // Convert markdown lists
  formatted = formatted.replace(/^\* (.*$)/gm, '<li>$1</li>');
  formatted = formatted.replace(/^- (.*$)/gm, '<li>$1</li>');
  
  // Convert markdown bold and italic
  formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');
  
  return formatted;
}

// Helper function to prepare file context for the prompt
function prepareFileContext(files: FileNode[]): string {
  if (!files || files.length === 0) {
    return "No files available in the current project.";
  }
  
  // Count files by type
  const fileTypes: Record<string, number> = {};
  let totalFiles = 0;
  
  function traverse(nodes: FileNode[]) {
    for (const node of nodes) {
      if (node.type === 'file') {
        totalFiles++;
        const extension = node.name.split('.').pop() || 'unknown';
        fileTypes[extension] = (fileTypes[extension] || 0) + 1;
      }
      if (node.type === 'folder' && node.children) {
        traverse(node.children);
      }
    }
  }
  
  traverse(files);
  
  // Create a summary of the project
  let context = `This project contains ${totalFiles} files.\n`;
  context += "File types:\n";
  
  Object.entries(fileTypes).forEach(([ext, count]) => {
    context += `- ${ext}: ${count} files\n`;
  });
  
  // Add some key files if available
  const importantExtensions = ['js', 'jsx', 'ts', 'tsx', 'py', 'java', 'html', 'css'];
  let importantFiles: string[] = [];
  
  function findImportantFiles(nodes: FileNode[], path = '') {
    for (const node of nodes) {
      const newPath = path ? `${path}/${node.name}` : node.name;
      
      if (node.type === 'file') {
        const extension = node.name.split('.').pop() || '';
        if (importantExtensions.includes(extension)) {
          importantFiles.push(newPath);
        }
      }
      
      if (node.type === 'folder' && node.children) {
        findImportantFiles(node.children, newPath);
      }
    }
  }
  
  findImportantFiles(files);
  
  if (importantFiles.length > 0) {
    context += "\nKey files in the project:\n";
    importantFiles.slice(0, 5).forEach(file => {
      context += `- ${file}\n`;
    });
  }
  
  return context;
}

function shouldIncludeContent(fileName: string): boolean {
  // Skip binary files, large files, etc.
  const excludedExtensions = ['.jpg', '.png', '.gif', '.svg', '.ico', '.woff', '.ttf'];
  const ext = fileName.split('.').pop()?.toLowerCase() || '';
  return !excludedExtensions.includes(`.${ext}`);
}

function truncateContent(content: string, maxLines: number): string {
  const lines = content.split('\n');
  if (lines.length <= maxLines) return content;
  
  return [...lines.slice(0, maxLines), '... (content truncated)'].join('\n');
}

function extractActionsFromResponse(response: string): OllamaResponse['actions'] {
  const actions: OllamaResponse['actions'] = [];
  
  // Look for ACTION blocks in the response
  const actionRegex = /ACTION:\s*-\s*title:\s*(.+?)\s*-\s*description:\s*(.+?)\s*-\s*type:\s*(\w+)/gs;
  
  let match;
  while ((match = actionRegex.exec(response)) !== null) {
    const title = match[1].trim();
    const description = match[2].trim();
    const type = match[3].trim();
    
    actions.push({
      title,
      description,
      status: 'complete',
    });
  }
  
  // If no specific actions were identified, try a simpler approach
  if (actions.length === 0) {
    // Try to identify actions from the response
    if (response.includes('analyze') || response.includes('review')) {
      actions.push({
        title: 'Code Analysis',
        description: 'Analyzed project structure and code',
        status: 'complete',
      });
    }
    
    if (response.includes('improve') || response.includes('refactor')) {
      actions.push({
        title: 'Code Improvement',
        description: 'Suggested code improvements',
        status: 'complete',
      });
    }
    
    if (response.includes('bug') || response.includes('fix')) {
      actions.push({
        title: 'Bug Detection',
        description: 'Identified potential issues',
        status: 'complete',
      });
    }
    
    // If still no actions, add a generic one
    if (actions.length === 0) {
      actions.push({
        title: 'Request Processing',
        description: 'Processed user request',
        status: 'complete',
      });
    }
  }
  
  return actions;
}

export async function getProjectStats(files: FileNode[]): Promise<ProjectStats> {
  // Count total files
  const totalFiles = countFiles(files);
  
  // Count total lines
  const totalLines = countLines(files);
  
  // Get file types
  const fileTypes = getFileTypes(files);
  
  // Mock issues count - in a real implementation this would analyze the code
  const issuesCount = Math.floor(Math.random() * 5);
  
  return {
    totalFiles,
    totalLines,
    fileTypes,
    issuesCount
  };
}

function countFiles(files: FileNode[]): number {
  let count = 0;
  
  function traverse(nodes: FileNode[]) {
    for (const node of nodes) {
      if (node.type === 'file') {
        count++;
      }
      if (node.type === 'folder' && node.children) {
        traverse(node.children);
      }
    }
  }
  
  traverse(files);
  return count;
}

function countLines(files: FileNode[]): number {
  let count = 0;
  
  function traverse(nodes: FileNode[]) {
    for (const node of nodes) {
      if (node.type === 'file' && node.content) {
        count += node.content.split('\n').length;
      }
      if (node.type === 'folder' && node.children) {
        traverse(node.children);
      }
    }
  }
  
  traverse(files);
  return count;
}

function getFileTypes(files: FileNode[]): { name: string; count: number }[] {
  const types: Record<string, number> = {};
  
  function traverse(nodes: FileNode[]) {
    for (const node of nodes) {
      if (node.type === 'file') {
        const extension = node.name.split('.').pop() || 'unknown';
        types[extension] = (types[extension] || 0) + 1;
      }
      if (node.type === 'folder' && node.children) {
        traverse(node.children);
      }
    }
  }
  
  traverse(files);
  
  return Object.entries(types).map(([name, count]) => ({ name, count }));
}
