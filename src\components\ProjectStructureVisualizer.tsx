import React, { useState, useEffect, useRef } from 'react';
import { FileNode } from './FileExplorer';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Download, ZoomIn, ZoomOut, RefreshCw } from 'lucide-react';

interface ProjectStructureVisualizerProps {
  files: FileNode[];
}

interface GraphNode {
  id: string;
  label: string;
  type: 'file' | 'folder' | 'dependency';
  color?: string;
  size?: number;
}

interface GraphEdge {
  from: string;
  to: string;
  label?: string;
  color?: string;
  width?: number;
}

interface GraphData {
  nodes: GraphNode[];
  edges: GraphEdge[];
}

const ProjectStructureVisualizer: React.FC<ProjectStructureVisualizerProps> = ({ files }) => {
  const [activeTab, setActiveTab] = useState('tree');
  const [graphData, setGraphData] = useState<GraphData>({ nodes: [], edges: [] });
  const [zoom, setZoom] = useState(1);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  // Generate graph data from files
  useEffect(() => {
    const data = generateGraphData(files);
    setGraphData(data);
  }, [files]);
  
  // Draw the graph when data or zoom changes
  useEffect(() => {
    if (activeTab === 'graph') {
      drawGraph();
    }
  }, [graphData, zoom, activeTab]);
  
  // Generate graph data from files
  const generateGraphData = (files: FileNode[]): GraphData => {
    const nodes: GraphNode[] = [];
    const edges: GraphEdge[] = [];
    const processedNodes = new Set<string>();
    
    // Process nodes recursively
    const processNode = (node: FileNode, parentId?: string) => {
      const nodeId = node.id;
      
      // Skip if already processed
      if (processedNodes.has(nodeId)) return;
      processedNodes.add(nodeId);
      
      // Add node
      nodes.push({
        id: nodeId,
        label: node.name,
        type: node.type,
        color: getNodeColor(node),
        size: node.type === 'folder' ? 15 : 10
      });
      
      // Add edge from parent
      if (parentId) {
        edges.push({
          from: parentId,
          to: nodeId,
          width: 1
        });
      }
      
      // Process children
      if (node.type === 'folder' && node.children) {
        node.children.forEach(child => processNode(child, nodeId));
      }
    };
    
    // Process all root nodes
    files.forEach(node => processNode(node));
    
    // Add dependency edges based on imports (simplified)
    // In a real implementation, this would analyze file content to find imports
    const fileNodes = files.flatMap(flattenNodes).filter(node => node.type === 'file');
    
    // Simulate some dependencies between files
    for (let i = 0; i < fileNodes.length; i++) {
      const file = fileNodes[i];
      
      // For JavaScript/TypeScript files, add some random dependencies
      if (file.name.endsWith('.js') || file.name.endsWith('.ts') || 
          file.name.endsWith('.jsx') || file.name.endsWith('.tsx')) {
        
        // Add 1-3 random dependencies
        const numDeps = Math.floor(Math.random() * 3) + 1;
        for (let j = 0; j < numDeps; j++) {
          const targetIndex = Math.floor(Math.random() * fileNodes.length);
          if (targetIndex !== i) {
            const target = fileNodes[targetIndex];
            edges.push({
              from: file.id,
              to: target.id,
              label: 'imports',
              color: '#999',
              width: 0.5
            });
          }
        }
      }
    }
    
    return { nodes, edges };
  };
  
  // Flatten the file tree into a list
  const flattenNodes = (node: FileNode): FileNode[] => {
    if (node.type === 'file') {
      return [node];
    } else if (node.type === 'folder' && node.children) {
      return [node, ...node.children.flatMap(flattenNodes)];
    }
    return [node];
  };
  
  // Get color based on node type and file extension
  const getNodeColor = (node: FileNode): string => {
    if (node.type === 'folder') {
      return '#4f46e5'; // Indigo for folders
    }
    
    // Color based on file extension
    const extension = node.name.split('.').pop()?.toLowerCase() || '';
    switch (extension) {
      case 'js':
        return '#eab308'; // Yellow for JavaScript
      case 'jsx':
        return '#3b82f6'; // Blue for JSX
      case 'ts':
        return '#0ea5e9'; // Light blue for TypeScript
      case 'tsx':
        return '#2563eb'; // Darker blue for TSX
      case 'css':
      case 'scss':
      case 'sass':
        return '#ec4899'; // Pink for styles
      case 'json':
        return '#84cc16'; // Green for JSON
      case 'md':
        return '#a855f7'; // Purple for markdown
      case 'html':
        return '#ef4444'; // Red for HTML
      default:
        return '#6b7280'; // Gray for other files
    }
  };
  
  // Draw the graph on canvas
  const drawGraph = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Set canvas dimensions
    canvas.width = canvas.clientWidth;
    canvas.height = canvas.clientHeight;
    
    // Calculate node positions using force-directed layout (simplified)
    const nodePositions = calculateNodePositions(graphData.nodes, graphData.edges, canvas.width, canvas.height);
    
    // Draw edges
    ctx.lineWidth = 1;
    graphData.edges.forEach(edge => {
      const sourcePos = nodePositions[edge.from];
      const targetPos = nodePositions[edge.to];
      
      if (sourcePos && targetPos) {
        ctx.beginPath();
        ctx.moveTo(sourcePos.x, sourcePos.y);
        ctx.lineTo(targetPos.x, targetPos.y);
        ctx.strokeStyle = edge.color || '#ccc';
        ctx.lineWidth = (edge.width || 1) * zoom;
        ctx.stroke();
        
        // Draw arrow
        const angle = Math.atan2(targetPos.y - sourcePos.y, targetPos.x - sourcePos.x);
        const arrowLength = 10 * zoom;
        const arrowWidth = 5 * zoom;
        
        ctx.beginPath();
        ctx.moveTo(targetPos.x, targetPos.y);
        ctx.lineTo(
          targetPos.x - arrowLength * Math.cos(angle) + arrowWidth * Math.sin(angle),
          targetPos.y - arrowLength * Math.sin(angle) - arrowWidth * Math.cos(angle)
        );
        ctx.lineTo(
          targetPos.x - arrowLength * Math.cos(angle) - arrowWidth * Math.sin(angle),
          targetPos.y - arrowLength * Math.sin(angle) + arrowWidth * Math.cos(angle)
        );
        ctx.closePath();
        ctx.fillStyle = edge.color || '#ccc';
        ctx.fill();
        
        // Draw edge label
        if (edge.label) {
          const midX = (sourcePos.x + targetPos.x) / 2;
          const midY = (sourcePos.y + targetPos.y) / 2;
          
          ctx.font = `${12 * zoom}px sans-serif`;
          ctx.fillStyle = '#666';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText(edge.label, midX, midY);
        }
      }
    });
    
    // Draw nodes
    graphData.nodes.forEach(node => {
      const pos = nodePositions[node.id];
      if (pos) {
        // Draw node circle
        ctx.beginPath();
        ctx.arc(pos.x, pos.y, (node.size || 10) * zoom, 0, Math.PI * 2);
        ctx.fillStyle = node.color || '#666';
        ctx.fill();
        
        // Draw node label
        ctx.font = `${12 * zoom}px sans-serif`;
        ctx.fillStyle = '#333';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(node.label, pos.x, pos.y + ((node.size || 10) + 10) * zoom);
      }
    });
  };
  
  // Calculate node positions using a simple force-directed layout
  const calculateNodePositions = (
    nodes: GraphNode[], 
    edges: GraphEdge[], 
    width: number, 
    height: number
  ): Record<string, { x: number; y: number }> => {
    const positions: Record<string, { x: number; y: number }> = {};
    
    // Initialize positions in a circle
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) * 0.4;
    
    nodes.forEach((node, i) => {
      const angle = (i / nodes.length) * Math.PI * 2;
      positions[node.id] = {
        x: centerX + radius * Math.cos(angle),
        y: centerY + radius * Math.sin(angle)
      };
    });
    
    // Apply force-directed layout (simplified)
    // In a real implementation, this would be more sophisticated
    const iterations = 50;
    const k = 0.1; // Spring constant
    const repulsion = 500; // Repulsion constant
    
    for (let iter = 0; iter < iterations; iter++) {
      // Calculate forces
      const forces: Record<string, { x: number; y: number }> = {};
      
      // Initialize forces
      nodes.forEach(node => {
        forces[node.id] = { x: 0, y: 0 };
      });
      
      // Repulsive forces between all nodes
      for (let i = 0; i < nodes.length; i++) {
        for (let j = i + 1; j < nodes.length; j++) {
          const nodeA = nodes[i];
          const nodeB = nodes[j];
          const posA = positions[nodeA.id];
          const posB = positions[nodeB.id];
          
          const dx = posB.x - posA.x;
          const dy = posB.y - posA.y;
          const distance = Math.sqrt(dx * dx + dy * dy) || 1;
          
          const force = repulsion / (distance * distance);
          const fx = (dx / distance) * force;
          const fy = (dy / distance) * force;
          
          forces[nodeA.id].x -= fx;
          forces[nodeA.id].y -= fy;
          forces[nodeB.id].x += fx;
          forces[nodeB.id].y += fy;
        }
      }
      
      // Attractive forces along edges
      edges.forEach(edge => {
        const sourcePos = positions[edge.from];
        const targetPos = positions[edge.to];
        
        if (sourcePos && targetPos) {
          const dx = targetPos.x - sourcePos.x;
          const dy = targetPos.y - sourcePos.y;
          const distance = Math.sqrt(dx * dx + dy * dy) || 1;
          
          const force = k * distance;
          const fx = (dx / distance) * force;
          const fy = (dy / distance) * force;
          
          forces[edge.from].x += fx;
          forces[edge.from].y += fy;
          forces[edge.to].x -= fx;
          forces[edge.to].y -= fy;
        }
      });
      
      // Apply forces
      nodes.forEach(node => {
        const force = forces[node.id];
        positions[node.id].x += force.x;
        positions[node.id].y += force.y;
        
        // Keep nodes within bounds
        positions[node.id].x = Math.max(50, Math.min(width - 50, positions[node.id].x));
        positions[node.id].y = Math.max(50, Math.min(height - 50, positions[node.id].y));
      });
    }
    
    return positions;
  };
  
  // Render tree view
  const renderTreeView = (nodes: FileNode[], level: number = 0): JSX.Element[] => {
    return nodes.map(node => {
      const indent = level * 20;
      const isFolder = node.type === 'folder';
      
      return (
        <React.Fragment key={node.id}>
          <div 
            className="flex items-center py-1 hover:bg-secondary/50 rounded px-2"
            style={{ marginLeft: `${indent}px` }}
          >
            <div 
              className="w-3 h-3 rounded-full mr-2"
              style={{ backgroundColor: getNodeColor(node) }}
            />
            <span className={isFolder ? 'font-medium' : ''}>{node.name}</span>
            {isFolder && <span className="text-xs text-muted-foreground ml-2">({node.children?.length || 0})</span>}
          </div>
          {isFolder && node.children && renderTreeView(node.children, level + 1)}
        </React.Fragment>
      );
    });
  };
  
  // Download graph as PNG
  const downloadGraph = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const link = document.createElement('a');
    link.download = 'project-structure.png';
    link.href = canvas.toDataURL('image/png');
    link.click();
  };
  
  // Zoom in
  const zoomIn = () => {
    setZoom(prev => Math.min(prev + 0.1, 2));
  };
  
  // Zoom out
  const zoomOut = () => {
    setZoom(prev => Math.max(prev - 0.1, 0.5));
  };
  
  // Reset zoom
  const resetZoom = () => {
    setZoom(1);
  };
  
  return (
    <Card className="w-full h-full">
      <CardContent className="p-4 h-full flex flex-col">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium">Project Structure</h3>
          <div className="flex items-center space-x-2">
            {activeTab === 'graph' && (
              <>
                <Button variant="outline" size="sm" onClick={zoomIn}>
                  <ZoomIn className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm" onClick={zoomOut}>
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm" onClick={resetZoom}>
                  <RefreshCw className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm" onClick={downloadGraph}>
                  <Download className="h-4 w-4" />
                </Button>
              </>
            )}
          </div>
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
          <TabsList className="mb-4">
            <TabsTrigger value="tree">Tree View</TabsTrigger>
            <TabsTrigger value="graph">Graph View</TabsTrigger>
          </TabsList>
          
          <TabsContent value="tree" className="flex-1 overflow-auto">
            <div className="space-y-1">
              {renderTreeView(files)}
            </div>
          </TabsContent>
          
          <TabsContent value="graph" className="flex-1 relative">
            <canvas 
              ref={canvasRef} 
              className="w-full h-full"
              style={{ cursor: 'move' }}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ProjectStructureVisualizer;