import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ExternalLink, Play, Square, RefreshCw, Globe, Server, Database } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AppService {
  id: string;
  name: string;
  port: number;
  status: 'stopped' | 'starting' | 'running' | 'error';
  url: string;
  type: 'web' | 'api' | 'database';
  description: string;
}

interface AppPreviewProps {
  selectedFile?: {
    id: string;
    name: string;
    content?: string;
    language?: string;
  } | null;
}

const AppPreview: React.FC<AppPreviewProps> = ({ selectedFile }) => {
  const [services, setServices] = useState<AppService[]>([
    {
      id: 'main-app',
      name: 'Ana Uygulama',
      port: 3000,
      status: 'stopped',
      url: '/demo-app.html',
      type: 'web',
      description: 'React/Node.js uygulaması'
    },
    {
      id: 'api-server',
      name: 'API Server',
      port: 3001,
      status: 'stopped',
      url: 'http://localhost:3001/api',
      type: 'api',
      description: 'REST API servisi'
    },
    {
      id: 'database',
      name: 'Database',
      port: 5432,
      status: 'stopped',
      url: 'postgresql://localhost:5432',
      type: 'database',
      description: 'PostgreSQL veritabanı'
    }
  ]);

  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const getServiceIcon = (type: AppService['type']) => {
    switch (type) {
      case 'web':
        return <Globe className="h-4 w-4" />;
      case 'api':
        return <Server className="h-4 w-4" />;
      case 'database':
        return <Database className="h-4 w-4" />;
      default:
        return <Server className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (status: AppService['status']) => {
    switch (status) {
      case 'running':
        return <Badge className="bg-green-100 text-green-800">Çalışıyor</Badge>;
      case 'starting':
        return <Badge variant="secondary">Başlatılıyor</Badge>;
      case 'error':
        return <Badge variant="destructive">Hata</Badge>;
      default:
        return <Badge variant="outline">Durduruldu</Badge>;
    }
  };

  const startService = async (serviceId: string) => {
    setServices(prev => prev.map(service => 
      service.id === serviceId 
        ? { ...service, status: 'starting' }
        : service
    ));

    // Simulate startup time
    await new Promise(resolve => setTimeout(resolve, 2000));

    setServices(prev => prev.map(service => 
      service.id === serviceId 
        ? { ...service, status: 'running' }
        : service
    ));

    // Set preview URL for web services
    const service = services.find(s => s.id === serviceId);
    if (service?.type === 'web') {
      setPreviewUrl(service.url);
    }
  };

  const stopService = (serviceId: string) => {
    setServices(prev => prev.map(service => 
      service.id === serviceId 
        ? { ...service, status: 'stopped' }
        : service
    ));

    // Clear preview if this was the previewed service
    const service = services.find(s => s.id === serviceId);
    if (service?.url === previewUrl) {
      setPreviewUrl(null);
    }
  };

  const startAllServices = async () => {
    // Start database first
    await startService('database');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Then API server
    await startService('api-server');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Finally main app
    await startService('main-app');
  };

  const stopAllServices = () => {
    services.forEach(service => {
      if (service.status === 'running') {
        stopService(service.id);
      }
    });
    setPreviewUrl(null);
  };

  const runSelectedFile = async () => {
    if (!selectedFile) return;

    if (selectedFile.name.includes('main')) {
      await startAllServices();
    } else {
      // Just run the specific file
      await startService('main-app');
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Play className="h-5 w-5" />
            <h2 className="font-medium">Uygulama Önizleme</h2>
          </div>
          <div className="flex gap-2">
            <Button 
              onClick={runSelectedFile} 
              disabled={!selectedFile}
              size="sm"
              className="bg-green-600 hover:bg-green-700"
            >
              <Play className="h-4 w-4 mr-2" />
              Çalıştır
            </Button>
            <Button 
              onClick={stopAllServices} 
              variant="outline"
              size="sm"
            >
              <Square className="h-4 w-4 mr-2" />
              Durdur
            </Button>
          </div>
        </div>

        {selectedFile && (
          <div className="text-sm text-muted-foreground">
            Dosya: {selectedFile.name}
          </div>
        )}
      </div>

      <div className="flex-1 flex flex-col">
        {/* Services Status */}
        <div className="p-4 border-b">
          <h3 className="font-medium mb-3">Servisler</h3>
          <div className="space-y-2">
            {services.map((service) => (
              <Card key={service.id} className="p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getServiceIcon(service.type)}
                    <div>
                      <div className="font-medium text-sm">{service.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {service.description} - Port {service.port}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(service.status)}
                    {service.status === 'running' && service.type === 'web' && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => window.open(service.url, '_blank')}
                      >
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Preview Area */}
        <div className="flex-1 p-4">
          {previewUrl ? (
            <div className="h-full flex flex-col">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  <span className="text-sm font-medium">Önizleme</span>
                  <Badge variant="outline" className="text-xs">{previewUrl}</Badge>
                </div>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => window.open(previewUrl, '_blank')}
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  Yeni Sekmede Aç
                </Button>
              </div>
              
              <div className="flex-1 border rounded-lg overflow-hidden bg-white">
                <iframe
                  src={previewUrl}
                  className="w-full h-full"
                  title="App Preview"
                  sandbox="allow-same-origin allow-scripts allow-forms"
                />
              </div>
            </div>
          ) : (
            <div className="h-full flex items-center justify-center text-muted-foreground">
              <div className="text-center">
                <Globe className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-sm">Uygulamayı çalıştırın ve önizlemeyi görün</p>
                <p className="text-xs mt-2">
                  Geliştirme ortamı: <code>localhost:8080</code><br/>
                  Uygulamanız: <code>localhost:3000</code>
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AppPreview;
