const express = require('express');
const http = require('http');
const { Server } = require('socket.io');
const cors = require('cors');
const os = require('os');
const path = require('path');
const fs = require('fs');

// Create Express app
const app = express();
app.use(cors());
app.use(express.json());

// Create HTTP server
const server = http.createServer(app);

// Create Socket.IO server
const io = new Server(server, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST']
  }
});

// Determine shell based on platform
const shell = process.platform === 'win32' ? 'powershell.exe' : 'bash';
const defaultCwd = process.platform === 'win32' ? process.env.USERPROFILE : process.env.HOME;

// Store active terminal sessions
const sessions = new Map();

// Generate a unique session ID
const generateSessionId = () => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

// Create a simulated terminal process
const createSimulatedTerminalProcess = (sessionId, options = {}) => {
  const {
    cwd = defaultCwd,
    cols = 80,
    rows = 24
  } = options;

  console.log(`Creating simulated terminal process for session ${sessionId}`);
  console.log(`CWD: ${cwd}, Cols: ${cols}, Rows: ${rows}`);

  // Ensure the working directory exists
  let workingDir = cwd;
  try {
    if (!fs.existsSync(workingDir)) {
      console.warn(`Directory ${workingDir} does not exist, falling back to default`);
      workingDir = defaultCwd;
    }
  } catch (error) {
    console.error(`Error checking directory: ${error.message}`);
    workingDir = defaultCwd;
  }

  // Store simulated files
  const simulatedFiles = {
    'hello.js': `console.log('Hello, world!');\nconsole.log('This is a simulated JavaScript file.');\n`,
    'test.py': `print("Hello from Python!")\nprint("This is a simulated Python file.")\n`,
    'app.ts': `function greet(name: string): string {\n  return \`Hello, \${name}!\`;\n}\n\nconsole.log(greet("TypeScript"));\n`,
    'index.html': `<!DOCTYPE html>\n<html>\n<head>\n  <title>Simulated HTML</title>\n</head>\n<body>\n  <h1>Hello, World!</h1>\n  <p>This is a simulated HTML file.</p>\n</body>\n</html>\n`,
    'styles.css': `body {\n  font-family: Arial, sans-serif;\n  background-color: #f0f0f0;\n  color: #333;\n}\n\nh1 {\n  color: blue;\n}\n`,
    'data.json': `{\n  "name": "Example",\n  "version": "1.0.0",\n  "description": "Simulated JSON file",\n  "items": [1, 2, 3, 4, 5]\n}\n`
  };

  // Function to run simulated code
  const runCode = (filename) => {
    const fileContent = simulatedFiles[filename];
    if (!fileContent) {
      return `\r\nError: File '${filename}' not found.\r\n`;
    }

    const extension = filename.split('.').pop().toLowerCase();

    switch (extension) {
      case 'js':
        simulatedProcess._dataCallback(`\r\n\x1b[1;32m# Running JavaScript file: ${filename}\x1b[0m\r\n`);
        // Parse and execute JavaScript code (simulated)
        if (filename === 'hello.js') {
          return `\r\nHello, world!\r\nThis is a simulated JavaScript file.\r\n`;
        } else {
          // Generic output for other JS files
          return `\r\n[JavaScript Output]\r\nExecuted ${filename} successfully.\r\n`;
        }

      case 'py':
        simulatedProcess._dataCallback(`\r\n\x1b[1;32m# Running Python file: ${filename}\x1b[0m\r\n`);
        // Parse and execute Python code (simulated)
        if (filename === 'test.py') {
          return `\r\nHello from Python!\r\nThis is a simulated Python file.\r\n`;
        } else {
          // Generic output for other Python files
          return `\r\n[Python Output]\r\nExecuted ${filename} successfully.\r\n`;
        }

      case 'ts':
        simulatedProcess._dataCallback(`\r\n\x1b[1;32m# Running TypeScript file: ${filename}\x1b[0m\r\n`);
        simulatedProcess._dataCallback(`\r\n\x1b[1;33mCompiling TypeScript...\x1b[0m\r\n`);
        // Parse and execute TypeScript code (simulated)
        if (filename === 'app.ts') {
          return `\r\nHello, TypeScript!\r\n`;
        } else {
          // Generic output for other TS files
          return `\r\n[TypeScript Output]\r\nCompiled and executed ${filename} successfully.\r\n`;
        }

      case 'html':
        simulatedProcess._dataCallback(`\r\n\x1b[1;32m# Opening HTML file: ${filename}\x1b[0m\r\n`);
        return `\r\nOpening ${filename} in browser (simulated).\r\nHTML content would be displayed in a browser window.\r\n`;

      default:
        return `\r\nCannot execute file with extension: ${extension}\r\n`;
    }
  };

  // Create a simulated terminal process
  const simulatedProcess = {
    pid: Math.floor(Math.random() * 10000),
    onData: (callback) => {
      simulatedProcess._dataCallback = callback;
      // Send initial welcome message
      callback(`Terminal running in simulation mode\r\n`);
      callback(`Current directory: ${workingDir}\r\n`);
      callback(`Type 'help' for available commands\r\n\r\n`);
      callback(`$ `);
    },
    onExit: (callback) => {
      simulatedProcess._exitCallback = callback;
    },
    write: (data) => {
      // Handle simulated commands
      if (data.includes('\r') || data.includes('\n')) {
        const command = simulatedProcess._currentCommand || '';
        simulatedProcess._currentCommand = '';

        if (command === 'help') {
          simulatedProcess._dataCallback(`\r\nAvailable commands:\r\n`);
          simulatedProcess._dataCallback(`  help                - Show this help\r\n`);
          simulatedProcess._dataCallback(`  ls                  - List files\r\n`);
          simulatedProcess._dataCallback(`  pwd                 - Show current directory\r\n`);
          simulatedProcess._dataCallback(`  echo <text>         - Echo text\r\n`);
          simulatedProcess._dataCallback(`  node <file.js>      - Run JavaScript file\r\n`);
          simulatedProcess._dataCallback(`  python <file.py>    - Run Python file\r\n`);
          simulatedProcess._dataCallback(`  ts-node <file.ts>   - Run TypeScript file\r\n`);
          simulatedProcess._dataCallback(`  cat <file>          - Show file content\r\n`);
          simulatedProcess._dataCallback(`  clear               - Clear screen\r\n`);
          simulatedProcess._dataCallback(`  exit                - Exit terminal\r\n\r\n`);
        } else if (command === 'ls') {
          simulatedProcess._dataCallback(`\r\nFiles in ${workingDir}:\r\n`);
          Object.keys(simulatedFiles).forEach(file => {
            simulatedProcess._dataCallback(`  ${file}\r\n`);
          });
          simulatedProcess._dataCallback(`\r\n`);
        } else if (command === 'pwd') {
          simulatedProcess._dataCallback(`\r\n${workingDir}\r\n\r\n`);
        } else if (command.startsWith('echo ')) {
          const text = command.substring(5);
          simulatedProcess._dataCallback(`\r\n${text}\r\n\r\n`);
        } else if (command.startsWith('node ')) {
          const filename = command.split(' ')[1];
          const output = runCode(filename);
          simulatedProcess._dataCallback(output);
        } else if (command.startsWith('python ')) {
          const filename = command.split(' ')[1];
          const output = runCode(filename);
          simulatedProcess._dataCallback(output);
        } else if (command.startsWith('ts-node ')) {
          const filename = command.split(' ')[1];
          const output = runCode(filename);
          simulatedProcess._dataCallback(output);
        } else if (command.startsWith('cat ')) {
          const filename = command.split(' ')[1];
          const fileContent = simulatedFiles[filename];

          if (fileContent) {
            simulatedProcess._dataCallback(`\r\n\x1b[1;33m# Content of ${filename}:\x1b[0m\r\n`);
            simulatedProcess._dataCallback(`\r\n${fileContent}\r\n`);
          } else {
            simulatedProcess._dataCallback(`\r\nError: File '${filename}' not found.\r\n`);
          }
        } else if (command === 'clear') {
          simulatedProcess._dataCallback('\x1b[2J\x1b[H');
        } else if (command === 'exit') {
          simulatedProcess._dataCallback('\r\nExiting terminal...\r\n');
          if (simulatedProcess._exitCallback) {
            simulatedProcess._exitCallback({ exitCode: 0, signal: null });
          }
          return;
        } else if (command !== '') {
          simulatedProcess._dataCallback(`\r\nCommand not found: ${command}\r\n\r\n`);
        }

        simulatedProcess._dataCallback(`$ `);
      } else {
        // Echo the character
        simulatedProcess._dataCallback(data);

        // Store the current command
        if (data === '\b') {
          // Handle backspace
          if (simulatedProcess._currentCommand && simulatedProcess._currentCommand.length > 0) {
            simulatedProcess._currentCommand = simulatedProcess._currentCommand.substring(0, simulatedProcess._currentCommand.length - 1);
          }
        } else {
          simulatedProcess._currentCommand = (simulatedProcess._currentCommand || '') + data;
        }
      }
    },
    resize: (cols, rows) => {
      console.log(`Simulated terminal resized to ${cols}x${rows}`);
    },
    kill: () => {
      console.log(`Simulated terminal killed`);
      if (simulatedProcess._exitCallback) {
        simulatedProcess._exitCallback({ exitCode: 0, signal: null });
      }
    },
    _currentCommand: ''
  };

  return simulatedProcess;
};

// Socket.IO connection handler
io.on('connection', (socket) => {
  const sessionId = generateSessionId();
  console.log(`New connection established: ${sessionId}`);

  // Store socket reference for direct communication
  socket.sessionId = sessionId;

  // Send initial connection acknowledgment
  socket.emit('connected', {
    sessionId,
    shell,
    platform: process.platform,
    defaultCwd
  });

  // Create a global event emitter for this session
  if (!global.eventEmitters) {
    global.eventEmitters = new Map();
  }
  global.eventEmitters.set(sessionId, socket);

  // Handle terminal creation
  socket.on('create', (options = {}) => {
    try {
      // Create a new simulated terminal process for this session
      const ptyProcess = createSimulatedTerminalProcess(sessionId, options);

      // Create simulated files
      const simulatedFiles = {
        'hello.js': `console.log('Hello, world!');\nconsole.log('This is a simulated JavaScript file.');\n`,
        'test.py': `print("Hello from Python!")\nprint("This is a simulated Python file.")\n`,
        'app.ts': `function greet(name: string): string {\n  return \`Hello, \${name}!\`;\n}\n\nconsole.log(greet("TypeScript"));\n`,
        'index.html': `<!DOCTYPE html>\n<html>\n<head>\n  <title>Simulated HTML</title>\n</head>\n<body>\n  <h1>Hello, World!</h1>\n  <p>This is a simulated HTML file.</p>\n</body>\n</html>\n`,
        'styles.css': `body {\n  font-family: Arial, sans-serif;\n  background-color: #f0f0f0;\n  color: #333;\n}\n\nh1 {\n  color: blue;\n}\n`,
        'data.json': `{\n  "name": "Example",\n  "version": "1.0.0",\n  "description": "Simulated JSON file",\n  "items": [1, 2, 3, 4, 5]\n}\n`
      };

      // Store the session
      sessions.set(sessionId, {
        socket,
        ptyProcess,
        options,
        simulatedFiles
      });

      // Send terminal output to client
      ptyProcess.onData((data) => {
        socket.emit('output', {
          type: 'output',
          content: data
        });
      });

      // Send terminal exit event to client
      ptyProcess.onExit(({ exitCode, signal }) => {
        socket.emit('exit', {
          type: 'exit',
          exitCode,
          signal
        });

        // Remove session on terminal exit
        if (sessions.has(sessionId)) {
          sessions.delete(sessionId);
        }
      });

      // Send acknowledgment
      socket.emit('created', {
        sessionId,
        pid: ptyProcess.pid
      });
    } catch (error) {
      console.error(`Error creating terminal: ${error.message}`);
      socket.emit('error', {
        type: 'error',
        message: `Failed to create terminal: ${error.message}`
      });
    }
  });

  // Handle terminal input
  socket.on('input', (data) => {
    if (!sessions.has(sessionId)) {
      socket.emit('error', {
        type: 'error',
        message: 'Terminal session not found'
      });
      return;
    }

    try {
      const { ptyProcess } = sessions.get(sessionId);
      ptyProcess.write(data.content);
    } catch (error) {
      console.error(`Error writing to terminal: ${error.message}`);
      socket.emit('error', {
        type: 'error',
        message: `Failed to write to terminal: ${error.message}`
      });
    }
  });

  // Handle terminal resize
  socket.on('resize', (data) => {
    if (!sessions.has(sessionId)) {
      socket.emit('error', {
        type: 'error',
        message: 'Terminal session not found'
      });
      return;
    }

    try {
      const { ptyProcess } = sessions.get(sessionId);
      ptyProcess.resize(data.cols, data.rows);

      // Update stored options
      const session = sessions.get(sessionId);
      session.options.cols = data.cols;
      session.options.rows = data.rows;

      socket.emit('resized', {
        cols: data.cols,
        rows: data.rows
      });
    } catch (error) {
      console.error(`Error resizing terminal: ${error.message}`);
      socket.emit('error', {
        type: 'error',
        message: `Failed to resize terminal: ${error.message}`
      });
    }
  });

  // Handle process kill request
  socket.on('kill', (data) => {
    if (!sessions.has(sessionId)) {
      socket.emit('error', {
        type: 'error',
        message: 'Terminal session not found'
      });
      return;
    }

    try {
      const { ptyProcess } = sessions.get(sessionId);

      if (data.pid && data.pid !== ptyProcess.pid) {
        // Kill specific process
        socket.emit('output', {
          type: 'output',
          content: `\r\nCannot kill process ${data.pid} in simulation mode\r\n`
        });
      } else {
        // Kill the terminal process
        ptyProcess.kill();
        sessions.delete(sessionId);
        socket.emit('killed', { sessionId });
      }
    } catch (error) {
      console.error(`Error killing process: ${error.message}`);
      socket.emit('error', {
        type: 'error',
        message: `Failed to kill process: ${error.message}`
      });
    }
  });

  // Handle client disconnection
  socket.on('disconnect', () => {
    console.log(`Connection closed: ${sessionId}`);

    // Kill the terminal process
    if (sessions.has(sessionId)) {
      const { ptyProcess } = sessions.get(sessionId);
      ptyProcess.kill();
      sessions.delete(sessionId);
    }
  });
});

// API routes
app.get('/api/status', (req, res) => {
  res.json({
    status: 'running',
    sessions: sessions.size,
    platform: process.platform,
    shell,
    defaultCwd,
    mode: 'simulation'
  });
});

app.get('/api/sessions', (req, res) => {
  const sessionInfo = Array.from(sessions.entries()).map(([id, { options, ptyProcess }]) => ({
    id,
    pid: ptyProcess.pid,
    cwd: options.cwd || defaultCwd,
    cols: options.cols || 80,
    rows: options.rows || 24
  }));

  res.json(sessionInfo);
});

// API route to get file content
app.get('/api/files/:filename', (req, res) => {
  const { filename } = req.params;

  // Get session from request
  const sessionId = req.query.sessionId;
  if (!sessionId || !sessions.has(sessionId)) {
    return res.status(404).json({ error: 'Session not found' });
  }

  const session = sessions.get(sessionId);
  const simulatedFiles = session.simulatedFiles || {};

  if (!simulatedFiles[filename]) {
    return res.status(404).json({ error: 'File not found' });
  }

  res.json({
    filename,
    content: simulatedFiles[filename]
  });
});

// API route to create or update a file
app.post('/api/files', (req, res) => {
  const { filename, content, sessionId } = req.body;

  if (!filename || content === undefined) {
    return res.status(400).json({ error: 'Filename and content are required' });
  }

  if (!sessionId || !sessions.has(sessionId)) {
    return res.status(404).json({ error: 'Session not found' });
  }

  const session = sessions.get(sessionId);

  // Initialize simulatedFiles if it doesn't exist
  if (!session.simulatedFiles) {
    session.simulatedFiles = {};
  }

  // Create or update the file
  session.simulatedFiles[filename] = content;

  // Notify the terminal about the file change
  if (session.ptyProcess && session.ptyProcess._dataCallback) {
    session.ptyProcess._dataCallback(`\r\n\x1b[1;32m# File ${filename} saved\x1b[0m\r\n$ `);
  }

  // Also send to all connected terminals via Socket.IO
  if (global.eventEmitters && global.eventEmitters.has(sessionId)) {
    const socket = global.eventEmitters.get(sessionId);
    socket.emit('output', {
      type: 'output',
      content: `\r\n\x1b[1;32m# File ${filename} saved\x1b[0m\r\n$ `
    });
  }

  // Broadcast to all terminals that a file has been updated
  io.emit('file-updated', {
    filename,
    sessionId
  });

  res.json({
    success: true,
    filename,
    message: `File ${filename} saved successfully`
  });
});

// API route to run code and get output
app.post('/api/run', (req, res) => {
  const { filename, sessionId } = req.body;

  if (!filename) {
    return res.status(400).json({ error: 'Filename is required' });
  }

  if (!sessionId || !sessions.has(sessionId)) {
    return res.status(404).json({ error: 'Session not found' });
  }

  const session = sessions.get(sessionId);
  const simulatedFiles = session.simulatedFiles || {};

  if (!simulatedFiles[filename]) {
    return res.status(404).json({ error: 'File not found' });
  }

  // Get file extension
  const extension = filename.split('.').pop().toLowerCase();

  // Simulate running the code
  let output = '';
  let success = true;

  switch (extension) {
    case 'js':
      output = `Running JavaScript file: ${filename}\n\n`;
      try {
        // Simulate JavaScript execution
        output += `Output:\n`;

        // Extract console.log statements
        const logRegex = /console\.log\(['"`](.+?)['"`]\)/g;
        const matches = simulatedFiles[filename].match(logRegex);

        if (matches && matches.length > 0) {
          matches.forEach(match => {
            try {
              const content = match.match(/console\.log\(['"`](.+?)['"`]\)/)[1];
              output += `${content}\n`;
            } catch (e) {
              // Skip if regex match fails
            }
          });
        } else {
          output += `[No output or could not parse console.log statements]\n`;
        }

        output += `\nJavaScript execution completed successfully.`;
      } catch (error) {
        output += `Error: ${error.message}`;
        success = false;
      }
      break;

    case 'py':
      output = `Running Python file: ${filename}\n\n`;
      try {
        // Simulate Python execution
        output += `Output:\n`;

        // Extract print statements
        const printRegex = /print\(["'](.+?)["']\)/g;
        const matches = simulatedFiles[filename].match(printRegex);

        if (matches && matches.length > 0) {
          matches.forEach(match => {
            try {
              const content = match.match(/print\(["'](.+?)["']\)/)[1];
              output += `${content}\n`;
            } catch (e) {
              // Skip if regex match fails
            }
          });
        } else {
          output += `[No output or could not parse print statements]\n`;
        }

        output += `\nPython execution completed successfully.`;
      } catch (error) {
        output += `Error: ${error.message}`;
        success = false;
      }
      break;

    case 'ts':
      output = `Running TypeScript file: ${filename}\n\n`;
      output += `Compiling TypeScript...\n`;
      try {
        // Simulate TypeScript execution
        output += `Output:\n`;

        // Extract console.log statements
        const logRegex = /console\.log\(['"`](.+?)['"`]\)/g;
        const matches = simulatedFiles[filename].match(logRegex);

        if (matches && matches.length > 0) {
          matches.forEach(match => {
            try {
              const content = match.match(/console\.log\(['"`](.+?)['"`]\)/)[1];
              output += `${content}\n`;
            } catch (e) {
              // Skip if regex match fails
            }
          });
        } else {
          output += `[No output or could not parse console.log statements]\n`;
        }

        output += `\nTypeScript compilation and execution completed successfully.`;
      } catch (error) {
        output += `Error: ${error.message}`;
        success = false;
      }
      break;

    case 'html':
      output = `Opening HTML file: ${filename}\n\n`;
      output += `HTML content would be displayed in a browser window.\n`;
      break;

    default:
      output = `Cannot execute file with extension: ${extension}`;
      success = false;
  }

  // Notify the terminal about the execution
  if (session.ptyProcess && session.ptyProcess._dataCallback) {
    session.ptyProcess._dataCallback(`\r\n\x1b[1;32m# Executed ${filename}\x1b[0m\r\n`);

    // Execute the command in the terminal
    const command = getCommandForFile(filename, extension);
    if (command) {
      session.ptyProcess._dataCallback(`$ ${command}\r\n`);

      // Send output to terminal
      output.split('\n').forEach(line => {
        session.ptyProcess._dataCallback(`${line}\r\n`);
      });

      session.ptyProcess._dataCallback(`\r\n$ `);
    }
  }

  // Also send to all connected terminals via Socket.IO
  if (global.eventEmitters && global.eventEmitters.has(sessionId)) {
    const socket = global.eventEmitters.get(sessionId);
    socket.emit('output', {
      type: 'output',
      content: `\r\n\x1b[1;32m# Executed ${filename}\x1b[0m\r\n$ ${getCommandForFile(filename, extension)}\r\n${output.replace(/\n/g, '\r\n')}\r\n$ `
    });
  }

  res.json({
    success,
    filename,
    output
  });
});

// Helper function to get command for file
function getCommandForFile(filename, extension) {
  switch (extension) {
    case 'js':
      return `node ${filename}`;
    case 'py':
      return `python ${filename}`;
    case 'ts':
      return `ts-node ${filename}`;
    case 'html':
      return `open ${filename}`;
    default:
      return null;
  }
}

// Handle server shutdown
process.on('SIGINT', () => {
  console.log('Shutting down terminal server...');

  // Kill all terminal processes
  for (const [sessionId, { ptyProcess }] of sessions.entries()) {
    console.log(`Killing terminal process for session ${sessionId}`);
    ptyProcess.kill();
  }

  // Close the server
  server.close(() => {
    console.log('Terminal server shut down');
    process.exit(0);
  });
});

// Start server
const PORT = process.env.PORT || 3002;
server.listen(PORT, () => {
  console.log(`Simple Terminal Server running on http://localhost:${PORT}`);
  console.log(`Socket.IO endpoint: ws://localhost:${PORT}`);
  console.log(`Running in simulation mode (no node-pty)`);
  console.log(`Default working directory: ${defaultCwd}`);
});
