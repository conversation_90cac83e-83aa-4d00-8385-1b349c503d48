// Express sunucusu
const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Ana sayfa
app.get('/', (req, res) => {
  res.send(`
    <html>
      <head>
        <title>Ollama Agent Server</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
          }
          h1 {
            color: #333;
          }
          .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
          }
        </style>
      </head>
      <body>
        <h1>Ollama Agent Server</h1>
        <div class="card">
          <h2>Server Status</h2>
          <p>Server is running on port ${PORT}</p>
          <p>Current time: ${new Date().toLocaleString()}</p>
        </div>
        <div class="card">
          <h2>API Endpoints</h2>
          <ul>
            <li><code>GET /api/status</code> - Server status</li>
            <li><code>GET /api/files</code> - List project files</li>
          </ul>
        </div>
      </body>
    </html>
  `);
});

// API endpoint - Status
app.get('/api/status', (req, res) => {
  res.json({
    status: 'online',
    timestamp: new Date(),
    uptime: process.uptime()
  });
});

// API endpoint - Files
app.get('/api/files', (req, res) => {
  const projectDir = path.join(__dirname);
  
  try {
    const files = fs.readdirSync(projectDir);
    res.json({
      directory: projectDir,
      files: files.map(file => ({
        name: file,
        isDirectory: fs.statSync(path.join(projectDir, file)).isDirectory()
      }))
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Sunucuyu başlat
app.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}`);
});