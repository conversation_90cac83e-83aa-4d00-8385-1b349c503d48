import { useRef, useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Sparkles, Settings, MessageSquare, Code, FileText, BookOpen, Terminal as TerminalIcon, Plus, Save, Play } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

import Sidebar from '@/components/Sidebar';
import FileExplorer, { FileNode } from '@/components/FileExplorer';
import MonacoEditor from '@/components/MonacoEditor';
import PromptInput from '@/components/PromptInput';
import ResponseDisplay from '@/components/ResponseDisplay';
import ProjectAnalytics from '@/components/ProjectAnalytics';
import ProjectIndexer from '@/components/ProjectIndexer';
import MCPServerManager from '@/components/MCPServerManager';
import SimpleTerminal from '@/components/SimpleTerminal';
import CodePreview from '@/components/CodePreview';
import ChatInterface from '@/components/ChatInterface';
import TestRunner from '@/components/TestRunner';
import { processPrompt, getProjectStats } from '@/services/ollamaService';
import { aiModelService } from '@/services/aiModelService';
import { analyzeCodebase } from '@/services/codeAnalysisService';
import { WebSearchService } from '@/services/webSearchService';
import { CodeGenerationService } from '@/services/codeGenerationService';

// Define the CodeGenerationOptions interface
interface CodeGenerationOptions {
  language: string;
  framework?: string;
  style?: 'functional' | 'class-based' | 'procedural';
  includeComments: boolean;
  includeTests: boolean;
  includeTypes: boolean;
  targetFile?: string;
}

const NewIndex = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('home');
  const [selectedFileId, setSelectedFileId] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showWelcomeDialog, setShowWelcomeDialog] = useState(false);
  const [response, setResponse] = useState('');
  const [chatMode, setChatMode] = useState<'chat' | 'command'>('chat');
  const [actions, setActions] = useState<Array<{
    title: string;
    description: string;
    status: 'pending' | 'complete' | 'error';
    output?: string;
  }>>([]);
  const [outputTab, setOutputTab] = useState<'response' | 'terminal' | 'chat' | 'test'>('chat');

  const [stats, setStats] = useState({
    totalFiles: 0,
    totalLines: 0,
    fileTypes: [] as { name: string; count: number }[],
    issuesCount: 0
  });

  // Get active AI model
  const activeModel = aiModelService.getActiveModel();

  // Initialize services
  const webSearchService = new WebSearchService();
  const codeGenerationService = new CodeGenerationService();

  // Load files from localStorage on component mount
  useEffect(() => {
    const savedFiles = localStorage.getItem('project-files');
    if (savedFiles) {
      try {
        setFiles(JSON.parse(savedFiles));
      } catch (error) {
        console.error('Failed to parse saved files:', error);
      }
    }
  }, []);

  // Sample project structure - in a real implementation this would be loaded from a backend
  const [files, setFiles] = useState<FileNode[]>([
    {
      id: 'root-1',
      name: 'src',
      type: 'folder',
      children: [
        {
          id: 'file-1',
          name: 'main.js',
          type: 'file',
          language: 'javascript',
          content: `// Main application entry point
import { initApp } from './app';
import { setupDatabase } from './database';

// Initialize application components
async function main() {
  try {
    await setupDatabase();
    await initApp();
    console.log('Application started successfully');
  } catch (error) {
    console.error('Failed to start application:', error);
    process.exit(1);
  }
}

main();`
        },
        {
          id: 'file-2',
          name: 'app.js',
          type: 'file',
          language: 'javascript',
          content: `// Application initialization
import { createServer } from 'http';
import { loadModules } from './modules';

export async function initApp() {
  const modules = await loadModules();

  const server = createServer((req, res) => {
    res.writeHead(200, { 'Content-Type': 'text/plain' });
    res.end('Ollama Agent Server');
  });

  server.listen(3000, () => {
    console.log('Server listening on port 3000');
  });

  return { server, modules };
}`
        },
        {
          id: 'folder-1',
          name: 'modules',
          type: 'folder',
          children: [
            {
              id: 'file-3',
              name: 'index.js',
              type: 'file',
              language: 'javascript',
              content: `// Module loader
import fs from 'fs';
import path from 'path';

export async function loadModules() {
  const moduleDir = path.join(__dirname);
  const modules = {};

  const files = fs.readdirSync(moduleDir);

  for (const file of files) {
    if (file === 'index.js') continue;
    if (file.endsWith('.js')) {
      const moduleName = file.replace('.js', '');
      modules[moduleName] = require(path.join(moduleDir, file));
    }
  }

  return modules;
}`
            },
            {
              id: 'file-4',
              name: 'fileScanner.js',
              type: 'file',
              language: 'javascript',
              content: `// File scanner module
import fs from 'fs';
import path from 'path';

export function scanDirectory(dirPath) {
  const results = [];

  function traverse(currentPath, relativePath = '') {
    const entries = fs.readdirSync(currentPath);

    for (const entry of entries) {
      const fullPath = path.join(currentPath, entry);
      const stats = fs.statSync(fullPath);

      if (stats.isDirectory()) {
        const newRelativePath = path.join(relativePath, entry);
        results.push({
          type: 'directory',
          name: entry,
          path: newRelativePath
        });
        traverse(fullPath, newRelativePath);
      } else {
        results.push({
          type: 'file',
          name: entry,
          path: path.join(relativePath, entry),
          size: stats.size
        });
      }
    }
  }

  traverse(dirPath);
  return results;
}`
            }
          ]
        },
        {
          id: 'file-5',
          name: 'database.js',
          type: 'file',
          language: 'javascript',
          content: `// Database setup and connection
import { createClient } from 'some-db-library';

let dbClient = null;

export async function setupDatabase() {
  try {
    dbClient = createClient({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      user: process.env.DB_USER || 'admin',
      password: process.env.DB_PASSWORD || 'password'
    });

    await dbClient.connect();
    console.log('Database connected successfully');
    return dbClient;
  } catch (error) {
    console.error('Database connection failed:', error);
    throw error;
  }
}

export function getDbClient() {
  if (!dbClient) {
    throw new Error('Database not initialized');
  }
  return dbClient;
}`
        }
      ]
    },
    {
      id: 'root-2',
      name: 'config',
      type: 'folder',
      children: [
        {
          id: 'file-6',
          name: 'default.json',
          type: 'file',
          language: 'json',
          content: `{
  "server": {
    "port": 3000,
    "host": "localhost"
  },
  "database": {
    "host": "localhost",
    "port": 5432,
    "user": "admin",
    "password": "password"
  },
  "logging": {
    "level": "info",
    "format": "json"
  }
}`
        }
      ]
    },
    {
      id: 'root-3',
      name: 'README.md',
      type: 'file',
      language: 'markdown',
      content: `# Ollama Agent

An intelligent AI agent that scans your codebase, understands the system architecture, and helps you develop new features, fix bugs, and improve your code.

## Features

- Project file scanning
- Code analysis
- Intelligent development assistance
- Module generation
- Bug detection and fixing

## Getting Started

1. Clone this repository
2. Install dependencies: \`npm install\`
3. Start the agent: \`npm start\`

## Usage

Provide a prompt describing what you want to do with your codebase, and the agent will:
- Analyze your project structure
- Understand the system architecture
- Make appropriate modifications
- Generate new code as needed
- Fix bugs and improve existing code`
    }
  ]);

  useEffect(() => {
    // Initialize project stats
    const updateStats = async () => {
      // Get basic project stats
      const projectStats = await getProjectStats(files);

      // Perform deeper code analysis
      try {
        const codeAnalysis = await analyzeCodebase(files);

        // Update stats with additional information from code analysis
        setStats({
          ...projectStats,
          totalFiles: codeAnalysis.projectStructure.fileCount,
          totalLines: Object.values(codeAnalysis.functions).length + Object.values(codeAnalysis.classes).length,
          fileTypes: Object.entries(codeAnalysis.projectStructure.fileTypes).map(([name, count]) => ({
            name,
            count: count as number
          })),
          issuesCount: 0
        });

        console.log('Code analysis completed:', codeAnalysis);
      } catch (error) {
        console.error('Error analyzing codebase:', error);
        setStats(projectStats);
      }
    };

    updateStats();
  }, [files]);

  // Find the selected file
  const selectedFile = findFileById(files, selectedFileId);

  // Helper function to find a file by ID
  function findFileById(nodes: FileNode[], id: string | null): FileNode | null {
    if (!id) return null;

    for (const node of nodes) {
      if (node.id === id) {
        return node;
      }
      if (node.type === 'folder' && node.children) {
        const found = findFileById(node.children, id);
        if (found) return found;
      }
    }

    return null;
  }

  const handleFileSelect = (file: FileNode) => {
    setSelectedFileId(file.id);
  };

  const handlePromptSubmit = async (prompt: string) => {
    setIsProcessing(true);
    setOutputTab('response');

    try {
      // Check if the prompt is asking for code generation
      if (prompt.toLowerCase().includes('generate') || prompt.toLowerCase().includes('create')) {
        // Determine language from prompt
        let language = 'typescript';
        if (prompt.toLowerCase().includes('javascript')) language = 'javascript';
        if (prompt.toLowerCase().includes('python')) language = 'python';
        if (prompt.toLowerCase().includes('java')) language = 'java';

        // Generate code using the code generation service
        const codeOptions: CodeGenerationOptions = {
          language,
          framework: prompt.toLowerCase().includes('react') ? 'react' : undefined,
          style: 'functional' as 'functional' | 'class-based' | 'procedural',
          includeComments: true,
          includeTests: prompt.toLowerCase().includes('test'),
          includeTypes: language === 'typescript'
        };

        try {
          const generatedCode = await codeGenerationService.generateCode(prompt, codeOptions, files);

          // Create a new file with the generated code
          const newFileId = `file-${Date.now()}`;
          const newFileName = generatedCode.targetFile;
          const newFile: FileNode = {
            id: newFileId,
            name: newFileName,
            type: 'file',
            language: generatedCode.language,
            content: generatedCode.code
          };

          // Add to root level
          setFiles(prevFiles => [...prevFiles, newFile]);

          // Select the new file
          setSelectedFileId(newFileId);

          // Set response
          setResponse(`Generated code in ${newFileName}:\n\n\`\`\`${generatedCode.language}\n${generatedCode.code}\n\`\`\``);
          setActions([{
            title: 'Dosya Kaydedildi',
            description: `Yeni dosya oluşturuldu: ${newFileName}`,
            status: 'complete'
          }]);

          toast({
            title: "Code Generated",
            description: `Generated ${newFileName} successfully.`,
          });

          return;
        } catch (error) {
          console.error('Error generating code:', error);
          // Continue with normal prompt processing if code generation fails
        }
      }

      // Standard prompt processing
      const result = await processPrompt(prompt, files);
      setResponse(result.response);
      setActions(result.actions || []);

      // Files can be updated through the editor interface
    } catch (error) {
      console.error('Error processing prompt:', error);
      setResponse('Sorry, there was an error processing your prompt.');
      toast({
        title: "Error",
        description: "Failed to process your prompt.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Helper function to update file content
  function updateFileContent(nodes: FileNode[], id: string, content: string): FileNode[] {
    return nodes.map(node => {
      if (node.id === id) {
        return { ...node, content };
      }
      if (node.type === 'folder' && node.children) {
        return {
          ...node,
          children: updateFileContent(node.children, id, content)
        };
      }
      return node;
    });
  }

  const handleFileChange = (fileId: string, content: string) => {
    setFiles(prevFiles => updateFileContent(prevFiles, fileId, content));
  };

  const handleNewFile = () => {
    const newFileId = `file-${Date.now()}`;
    const newFileName = 'new-file.js';
    const newFile: FileNode = {
      id: newFileId,
      name: newFileName,
      type: 'file',
      language: 'javascript',
      content: '// New file\n'
    };

    setFiles(prevFiles => [...prevFiles, newFile]);
    setSelectedFileId(newFileId);
    
    toast({
      title: "New File Created",
      description: `Created ${newFileName} successfully.`,
    });
  };

  const handleDeleteFile = () => {
    if (!selectedFileId) {
      toast({
        title: "No File Selected",
        description: "Please select a file to delete.",
        variant: "destructive",
      });
      return;
    }

    setFiles(prevFiles => prevFiles.filter(file => 
      file.id !== selectedFileId && 
      (file.type !== 'folder' || !findFileInFolder(file, selectedFileId))
    ));
    
    setSelectedFileId(null);
    
    toast({
      title: "File Deleted",
      description: "The file has been deleted successfully.",
    });
  };

  // Helper function to check if a file exists in a folder
  function findFileInFolder(folder: FileNode, fileId: string): boolean {
    if (folder.type !== 'folder' || !folder.children) return false;
    
    for (const child of folder.children) {
      if (child.id === fileId) return true;
      if (child.type === 'folder' && findFileInFolder(child, fileId)) return true;
    }
    
    return false;
  }

  const handleSaveChanges = () => {
    // Save files to localStorage
    localStorage.setItem('project-files', JSON.stringify(files));
    
    toast({
      title: "Changes Saved",
      description: "Your changes have been saved successfully.",
    });
  };

  const handleRunCode = () => {
    if (!selectedFileId || !selectedFile) {
      toast({
        title: "Dosya Seçilmedi",
        description: "Lütfen çalıştırmak için bir dosya seçin.",
        variant: "destructive",
      });
      return;
    }

    // Switch to terminal tab to show the output
    setOutputTab('terminal');

    toast({
      title: "Kod Çalıştırılıyor",
      description: `${selectedFile.name} dosyası çalıştırılıyor...`,
    });
  };

  return (
    <div className="flex h-screen overflow-hidden bg-background">
      <Sidebar 
        activeTab={activeTab}
        onChangeTab={setActiveTab}
        onNewFile={handleNewFile}
        onDeleteFile={handleDeleteFile}
        onSaveChanges={handleSaveChanges}
        onRunCode={handleRunCode}
        selectedFileId={selectedFileId}
      />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Main Content */}
        <div className="flex-1 overflow-hidden">
          <ResizablePanelGroup direction="horizontal">
            {/* Left Panel - Files and Explorer */}
            <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
              <Tabs defaultValue="files" className="h-full flex flex-col">
                <div className="border-b px-3 py-2">
                  <TabsList className="w-full grid grid-cols-2">
                    <TabsTrigger value="files">Dosyalar</TabsTrigger>
                    <TabsTrigger value="structure">Yapı</TabsTrigger>
                  </TabsList>
                </div>
                
                <ScrollArea className="flex-1">
                  <TabsContent value="files" className="h-full m-0 p-0">
                    <FileExplorer 
                      files={files}
                      onFileSelect={handleFileSelect}
                      selectedFileId={selectedFileId}
                    />
                  </TabsContent>
                  <TabsContent value="structure" className="h-full p-4 m-0">
                    <div className="text-sm">
                      <h3 className="font-medium mb-2">Proje Yapısı</h3>
                      <div className="text-muted-foreground">
                        <p className="mb-2">Dosya sayısı: {stats.totalFiles}</p>
                        <p className="mb-2">Kod satırı: {stats.totalLines}</p>
                        
                        <h4 className="font-medium mt-4 mb-2">Dosya Türleri</h4>
                        <div className="space-y-1">
                          {stats.fileTypes.map((type, index) => (
                            <div key={index} className="flex justify-between">
                              <span>{type.name}</span>
                              <span>{type.count}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </ScrollArea>
              </Tabs>
            </ResizablePanel>
            
            <ResizableHandle withHandle />
            
            {/* Center Panel - Editor or Dashboard */}
            <ResizablePanel defaultSize={50}>
              {selectedFile ? (
                <div className="h-full flex flex-col">
                  <div className="border-b px-4 py-2 flex items-center justify-between">
                    <div className="flex items-center">
                      {selectedFile.language === 'javascript' ? <FileText className="h-4 w-4 text-yellow-500 mr-2" /> : 
                       selectedFile.language === 'typescript' ? <FileText className="h-4 w-4 text-blue-500 mr-2" /> :
                       selectedFile.language === 'json' ? <FileText className="h-4 w-4 text-green-500 mr-2" /> :
                       <FileText className="h-4 w-4 mr-2" />}
                      <span className="font-medium text-sm">{selectedFile.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="sm" onClick={handleSaveChanges}>
                        <Save className="h-4 w-4 mr-1" />
                        Kaydet
                      </Button>
                      <Button variant="ghost" size="sm" onClick={handleRunCode}>
                        <Play className="h-4 w-4 mr-1" />
                        Çalıştır
                      </Button>
                    </div>
                  </div>
                  <div className="flex-1 overflow-hidden">
                    <MonacoEditor
                      file={selectedFile}
                      onContentChange={handleFileChange}
                    />
                  </div>
                </div>
              ) : (
                <div className="h-full flex flex-col">
                  <div className="border-b px-4 py-2">
                    <h2 className="font-medium">Gösterge Paneli</h2>
                  </div>
                  <ScrollArea className="flex-1 p-4">
                    <div className="grid gap-4 md:grid-cols-2 xl:grid-cols-3">
                      <Button 
                        variant="outline" 
                        className="h-24 flex-col items-center justify-center border-dashed text-muted-foreground hover:text-foreground"
                        onClick={handleNewFile}
                      >
                        <Code className="mb-2 h-5 w-5" />
                        Yeni Dosya Oluştur
                      </Button>
                      
                      <Button 
                        variant="outline" 
                        className="h-24 flex-col items-center justify-center border-dashed text-muted-foreground hover:text-foreground"
                        onClick={() => setOutputTab('terminal')}
                      >
                        <TerminalIcon className="mb-2 h-5 w-5" />
                        Terminal Aç
                      </Button>
                      
                      <Button 
                        variant="outline" 
                        className="h-24 flex-col items-center justify-center border-dashed text-primary/70 hover:text-primary"
                        onClick={() => setOutputTab('chat')}
                      >
                        <Sparkles className="mb-2 h-5 w-5" />
                        Kod Oluştur
                      </Button>
                    </div>
                    
                    <div className="mt-8">
                      <h2 className="text-lg font-semibold mb-4">Proje İstatistikleri</h2>
                      <div className="grid gap-4 md:grid-cols-4">
                        <div className="rounded-lg border bg-card p-4">
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4 text-muted-foreground" />
                            <div className="text-sm font-medium">Toplam Dosya</div>
                          </div>
                          <div className="text-2xl font-bold mt-2">{stats.totalFiles}</div>
                        </div>
                        
                        <div className="rounded-lg border bg-card p-4">
                          <div className="flex items-center gap-2">
                            <Code className="h-4 w-4 text-muted-foreground" />
                            <div className="text-sm font-medium">Toplam Satır</div>
                          </div>
                          <div className="text-2xl font-bold mt-2">{stats.totalLines}</div>
                        </div>
                        
                        <div className="rounded-lg border bg-card p-4">
                          <div className="flex items-center gap-2">
                            <BookOpen className="h-4 w-4 text-muted-foreground" />
                            <div className="text-sm font-medium">Dosya Tipi</div>
                          </div>
                          <div className="text-2xl font-bold mt-2">{stats.fileTypes.length}</div>
                        </div>
                        
                        <div className="rounded-lg border bg-card p-4">
                          <div className="flex items-center gap-2">
                            <Sparkles className="h-4 w-4 text-primary" />
                            <div className="text-sm font-medium">AI Model</div>
                          </div>
                          <div className="text-xl font-bold mt-2 truncate">{activeModel?.name || "Varsayılan"}</div>
                        </div>
                      </div>
                      
                      <ProjectAnalytics stats={stats} />
                    </div>
                  </ScrollArea>
                </div>
              )}
            </ResizablePanel>
            
            <ResizableHandle withHandle />
            
            {/* Right Panel - Chat or Response */}
            <ResizablePanel defaultSize={30} minSize={25} maxSize={40}>
              <Tabs defaultValue="chat" className="h-full flex flex-col" onValueChange={(value) => setOutputTab(value as any)}>
                <div className="border-b px-3 py-2">
                  <TabsList className="w-full grid grid-cols-4">
                    <TabsTrigger value="chat">Sohbet</TabsTrigger>
                    <TabsTrigger value="response">Yanıt</TabsTrigger>
                    <TabsTrigger value="terminal">Terminal</TabsTrigger>
                    <TabsTrigger value="test">Test</TabsTrigger>
                  </TabsList>
                </div>
                
                <TabsContent value="chat" className="flex-1 overflow-hidden m-0 p-0">
                  <ChatInterface 
                    onSendMessage={async (message) => {
                      try {
                        setIsProcessing(true);
                        const result = await processPrompt(message, files);
                        setResponse(result.response);
                        setActions(result.actions || []);
                        return result.response;
                      } catch (error) {
                        console.error('Error processing message:', error);
                        return 'Üzgünüm, mesajınızı işlerken bir hata oluştu.';
                      } finally {
                        setIsProcessing(false);
                      }
                    }}
                  />
                </TabsContent>
                
                <TabsContent value="response" className="flex-1 overflow-hidden m-0 p-0">
                  <div className="h-full flex flex-col">
                    <div className="p-3 border-b">
                      <h2 className="font-medium text-sm">Komut Yanıtı</h2>
                    </div>
                    <div className="flex-1">
                      <ResponseDisplay response={response} actions={actions} />
                    </div>
                    <PromptInput onSubmit={handlePromptSubmit} isProcessing={isProcessing} />
                  </div>
                </TabsContent>
                
                <TabsContent value="terminal" className="flex-1 overflow-hidden m-0 p-0">
                  <SimpleTerminal
                    onExecuteCommand={async (command) => {
                      // Smart command execution based on selected file and command
                      const cmd = command.trim().toLowerCase();

                      if (cmd.startsWith('run') || cmd.startsWith('node')) {
                        if (selectedFile) {
                          if (selectedFile.name.includes('main')) {
                            return `> ${selectedFile.name} çalıştırılıyor...

Database bağlantısı kuruluyor...
✓ Database başarıyla bağlandı

Server başlatılıyor...
✓ Server http://localhost:3000 adresinde çalışıyor

Uygulama başarıyla başlatıldı!`;
                          } else {
                            return `> ${selectedFile.name} çalıştırılıyor...

Dosya işleniyor...
✓ ${selectedFile.name} başarıyla çalıştırıldı
Process completed with exit code 0`;
                          }
                        } else {
                          return `Hata: Çalıştırılacak dosya seçilmedi.`;
                        }
                      }

                      if (cmd.includes('test')) {
                        return `> Test suite çalıştırılıyor...

Jest test runner başlatılıyor...
✓ Syntax testleri geçti
✓ Unit testler geçti
✓ Integration testler geçti

Test Results:
  Passed: 12
  Failed: 0
  Total: 12

Tüm testler başarılı! 🎉`;
                      }

                      if (cmd.includes('build')) {
                        return `> Production build oluşturuluyor...

TypeScript derleniyor...
✓ TypeScript compilation successful

Vite build çalıştırılıyor...
✓ Assets optimized
✓ Bundle created

Build başarıyla tamamlandı!
Output: dist/`;
                      }

                      return `Komut çalıştırıldı: ${command}`;
                    }}
                  />
                </TabsContent>

                <TabsContent value="test" className="flex-1 overflow-hidden m-0 p-0">
                  <TestRunner selectedFile={selectedFile} />
                </TabsContent>
              </Tabs>
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>
      </div>
      
      {/* Welcome Dialog */}
      <Dialog open={showWelcomeDialog} onOpenChange={setShowWelcomeDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-primary" />
              AI Kod Asistanına Hoş Geldiniz
            </DialogTitle>
            <DialogDescription>
              Bu araç, kod geliştirme sürecinizde size yardımcı olmak için tasarlanmıştır.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <p className="text-sm text-muted-foreground">
              AI Kod Asistanı, projenizi analiz ederek size özel tavsiyeler sunar, yeni özellikler geliştirir ve kodunuzu iyileştirir.
            </p>
            <div className="rounded-md bg-muted p-4">
              <h4 className="text-sm font-medium mb-2">Örnek komutlar:</h4>
              <ul className="text-sm space-y-2">
                <li className="flex items-start gap-2">
                  <span className="bg-primary/20 text-primary rounded-full p-1">
                    <Sparkles className="h-3 w-3" />
                  </span>
                  <span>"Bu projeye bir kullanıcı kimlik doğrulama sistemi ekle"</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="bg-primary/20 text-primary rounded-full p-1">
                    <Sparkles className="h-3 w-3" />
                  </span>
                  <span>"Bu kodda hata var, düzelt: [kodunuzu buraya ekleyin]"</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="bg-primary/20 text-primary rounded-full p-1">
                    <Sparkles className="h-3 w-3" />
                  </span>
                  <span>"Bu fonksiyonun performansını iyileştir"</span>
                </li>
              </ul>
            </div>
          </div>
          <Button onClick={() => setShowWelcomeDialog(false)}>
            Başlayalım
          </Button>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default NewIndex;