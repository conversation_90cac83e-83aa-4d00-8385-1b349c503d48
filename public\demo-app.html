<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Uygulama - localhost:3000</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .feature h3 {
            margin-bottom: 0.5rem;
            color: #fff;
        }
        
        .feature p {
            opacity: 0.8;
            font-size: 0.9rem;
        }
        
        .status {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            border: 1px solid rgba(34, 197, 94, 0.3);
            margin: 1rem 0;
        }
        
        .status::before {
            content: '●';
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .info {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 10px;
            margin-top: 2rem;
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .port-info {
            display: flex;
            justify-content: space-around;
            margin: 1rem 0;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .port {
            background: rgba(255, 255, 255, 0.1);
            padding: 0.8rem;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .port strong {
            display: block;
            margin-bottom: 0.3rem;
        }
        
        .port code {
            background: rgba(0, 0, 0, 0.2);
            padding: 0.2rem 0.5rem;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Demo Uygulama</h1>
        <p class="subtitle">AI Kod Asistanı tarafından oluşturulan örnek uygulama</p>
        
        <div class="status">
            <span>Uygulama Çalışıyor</span>
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>⚡ Hızlı</h3>
                <p>Modern teknolojilerle geliştirildi</p>
            </div>
            <div class="feature">
                <h3>🎨 Güzel</h3>
                <p>Kullanıcı dostu arayüz</p>
            </div>
            <div class="feature">
                <h3>🔧 Esnek</h3>
                <p>Kolay özelleştirilebilir</p>
            </div>
        </div>
        
        <div class="port-info">
            <div class="port">
                <strong>Geliştirme Ortamı</strong>
                <code>localhost:8080</code>
            </div>
            <div class="port">
                <strong>Bu Uygulama</strong>
                <code>localhost:3000</code>
            </div>
        </div>
        
        <div class="info">
            <p><strong>Bu bir demo uygulamasıdır.</strong></p>
            <p>Gerçek bir projede, bu sayfa yerine React, Vue, Angular veya başka bir framework ile oluşturulmuş uygulamanız görünecektir.</p>
            <br>
            <p>AI Kod Asistanı ile geliştirdiğiniz uygulamalar burada önizlenebilir ve test edilebilir.</p>
        </div>
    </div>
    
    <script>
        // Simple demo functionality
        console.log('Demo uygulama başlatıldı!');
        console.log('Port: 3000');
        console.log('Geliştirme ortamı: localhost:8080');
        
        // Simulate some activity
        setInterval(() => {
            console.log('Uygulama çalışıyor...', new Date().toLocaleTimeString());
        }, 5000);
    </script>
</body>
</html>
