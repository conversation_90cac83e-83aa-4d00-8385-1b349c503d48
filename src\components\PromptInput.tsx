
import React, { useState, useEffect } from 'react';
import { Send, Loader2, <PERSON><PERSON><PERSON>, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';

interface PromptInputProps {
  onSubmit: (prompt: string) => Promise<void>;
  isProcessing: boolean;
}

const PromptInput: React.FC<PromptInputProps> = ({ onSubmit, isProcessing }) => {
  const [prompt, setPrompt] = useState('');
  const [promptHistory, setPromptHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  // Load prompt history from localStorage on component mount
  useEffect(() => {
    const savedHistory = localStorage.getItem('prompt-history');
    if (savedHistory) {
      setPromptHistory(JSON.parse(savedHistory));
    }
  }, []);

  const handleSubmit = async () => {
    if (!prompt.trim() || isProcessing) return;
    
    try {
      // Save to history
      const newHistory = [prompt, ...promptHistory.slice(0, 19)]; // Keep last 20 prompts
      setPromptHistory(newHistory);
      localStorage.setItem('prompt-history', JSON.stringify(newHistory));
      setHistoryIndex(-1);
      
      await onSubmit(prompt);
      setPrompt('');
    } catch (error) {
      console.error('Error processing prompt:', error);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      e.preventDefault();
      handleSubmit();
    } else if (e.key === 'ArrowUp' && e.ctrlKey) {
      e.preventDefault();
      // Navigate up through history
      if (promptHistory.length > 0 && historyIndex < promptHistory.length - 1) {
        const newIndex = historyIndex + 1;
        setHistoryIndex(newIndex);
        setPrompt(promptHistory[newIndex]);
      }
    } else if (e.key === 'ArrowDown' && e.ctrlKey) {
      e.preventDefault();
      // Navigate down through history
      if (historyIndex > 0) {
        const newIndex = historyIndex - 1;
        setHistoryIndex(newIndex);
        setPrompt(promptHistory[newIndex]);
      } else if (historyIndex === 0) {
        setHistoryIndex(-1);
        setPrompt('');
      }
    }
  };

  const clearPrompt = () => {
    setPrompt('');
    setHistoryIndex(-1);
  };

  return (
    <div className="border-t border-border p-4 bg-background/80 backdrop-blur-sm">
      <div className="flex items-start gap-3">
        <div className="relative flex-1">
          <Textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Yapay zekaya ne sormak istersiniz? Kod oluşturma, analiz, açıklama..."
            className={cn(
              "flex-1 min-h-[80px] max-h-[200px] resize-none bg-muted/30 pr-10",
              "focus:ring-1 focus:ring-offset-0 focus:ring-primary modern-input"
            )}
            disabled={isProcessing}
          />
          {prompt.length > 0 ? (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 top-2 h-6 w-6 text-muted-foreground hover:text-foreground"
              onClick={clearPrompt}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          ) : (
            <Sparkles 
              className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" 
            />
          )}
        </div>
        <Button 
          onClick={handleSubmit}
          className="h-10 px-4 modern-button"
          disabled={!prompt.trim() || isProcessing}
        >
          {isProcessing ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Send className="h-4 w-4" />
          )}
          <span className="ml-2">Gönder</span>
        </Button>
      </div>
      <div className="text-xs text-muted-foreground mt-2 flex items-center justify-between">
        <div>
          <span className="mr-4">
            <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Ctrl</kbd>+<kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Enter</kbd> gönder
          </span>
          <span>
            <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Ctrl</kbd>+<kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">↑/↓</kbd> geçmiş
          </span>
        </div>
        <div className="text-right">
          <span className="text-primary">AI-powered</span> kod asistanı
        </div>
      </div>
    </div>
  );
};

export default PromptInput;
