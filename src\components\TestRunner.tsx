import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Play, CheckCircle, XCircle, Clock, FileText } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TestResult {
  id: string;
  name: string;
  status: 'pending' | 'running' | 'passed' | 'failed';
  duration?: number;
  error?: string;
  output?: string;
}

interface TestRunnerProps {
  selectedFile?: {
    id: string;
    name: string;
    content?: string;
    language?: string;
    type: 'file' | 'folder';
  } | null;
}

const TestRunner: React.FC<TestRunnerProps> = ({ selectedFile }) => {
  const [tests, setTests] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const generateTests = (file: any) => {
    if (!file) return [];

    const testCases: TestResult[] = [];

    // Generate tests based on file type and content
    if (file.language === 'javascript' || file.name.endsWith('.js')) {
      testCases.push(
        {
          id: 'syntax-check',
          name: 'Syntax Kontrolü',
          status: 'pending'
        },
        {
          id: 'function-test',
          name: 'Fonksiyon Testleri',
          status: 'pending'
        },
        {
          id: 'error-handling',
          name: 'Hata Yönetimi',
          status: 'pending'
        }
      );
    }

    if (file.content.includes('import') || file.content.includes('require')) {
      testCases.push({
        id: 'dependency-check',
        name: 'Bağımlılık Kontrolü',
        status: 'pending'
      });
    }

    if (file.content.includes('async') || file.content.includes('await')) {
      testCases.push({
        id: 'async-test',
        name: 'Asenkron İşlem Testleri',
        status: 'pending'
      });
    }

    return testCases;
  };

  const runTests = async () => {
    if (!selectedFile) return;

    const testCases = generateTests(selectedFile);
    setTests(testCases);
    setIsRunning(true);

    // Simulate running tests
    for (let i = 0; i < testCases.length; i++) {
      const test = testCases[i];
      
      // Update test status to running
      setTests(prev => prev.map(t => 
        t.id === test.id ? { ...t, status: 'running' } : t
      ));

      // Simulate test execution time
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

      // Simulate test result
      const passed = Math.random() > 0.2; // 80% pass rate
      const duration = Math.floor(100 + Math.random() * 500);

      setTests(prev => prev.map(t => 
        t.id === test.id ? {
          ...t,
          status: passed ? 'passed' : 'failed',
          duration,
          error: passed ? undefined : 'Test başarısız oldu',
          output: passed ? 'Test başarıyla geçti' : 'Beklenen sonuç alınamadı'
        } : t
      ));
    }

    setIsRunning(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'running':
        return <Clock className="h-4 w-4 animate-spin text-yellow-500" />;
      case 'passed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <div className="h-4 w-4 rounded-full border-2 border-gray-300" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'running':
        return <Badge variant="secondary">Çalışıyor</Badge>;
      case 'passed':
        return <Badge className="bg-green-100 text-green-800">Başarılı</Badge>;
      case 'failed':
        return <Badge variant="destructive">Başarısız</Badge>;
      default:
        return <Badge variant="outline">Bekliyor</Badge>;
    }
  };

  const passedTests = tests.filter(t => t.status === 'passed').length;
  const failedTests = tests.filter(t => t.status === 'failed').length;
  const totalTests = tests.length;

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            <h2 className="font-medium">Test Çalıştırıcı</h2>
          </div>
          <Button 
            onClick={runTests} 
            disabled={!selectedFile || isRunning}
            size="sm"
          >
            <Play className="h-4 w-4 mr-2" />
            {isRunning ? 'Çalışıyor...' : 'Testleri Çalıştır'}
          </Button>
        </div>

        {selectedFile && (
          <div className="text-sm text-muted-foreground">
            Dosya: {selectedFile.name}
          </div>
        )}

        {tests.length > 0 && (
          <div className="flex gap-4 mt-2 text-sm">
            <span className="text-green-600">✓ {passedTests} Başarılı</span>
            <span className="text-red-600">✗ {failedTests} Başarısız</span>
            <span className="text-gray-600">Toplam: {totalTests}</span>
          </div>
        )}
      </div>

      <ScrollArea className="flex-1 p-4">
        {!selectedFile ? (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            Test çalıştırmak için bir dosya seçin
          </div>
        ) : tests.length === 0 ? (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            "Testleri Çalıştır" butonuna tıklayın
          </div>
        ) : (
          <div className="space-y-3">
            {tests.map((test) => (
              <Card key={test.id} className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(test.status)}
                    <span className="font-medium">{test.name}</span>
                  </div>
                  {getStatusBadge(test.status)}
                </div>

                {test.duration && (
                  <div className="text-xs text-muted-foreground mb-2">
                    Süre: {test.duration}ms
                  </div>
                )}

                {test.output && (
                  <div className={cn(
                    "text-xs p-2 rounded",
                    test.status === 'passed'
                      ? "bg-green-50 text-green-700"
                      : "bg-red-50 text-red-700"
                  )}>
                    {test.output}
                  </div>
                )}

                {test.error && (
                  <div className="text-sm text-red-600 mt-2">
                    Hata: {test.error}
                  </div>
                )}
              </Card>
            ))}
          </div>
        )}
      </ScrollArea>
    </div>
  );
};

export default TestRunner;
