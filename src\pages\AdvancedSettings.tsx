import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, Save, RefreshCw } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';

import AIModelManager from '@/components/AIModelManager';
import PromptTemplateManager from '@/components/PromptTemplateManager';
import ProjectStructureVisualizer from '@/components/ProjectStructureVisualizer';
import { FileNode } from '@/components/FileExplorer';

const AdvancedSettings: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('ai-models');
  const [isSaving, setIsSaving] = useState(false);
  
  // Load files from localStorage
  const loadFiles = (): FileNode[] => {
    try {
      const savedFiles = localStorage.getItem('project-files');
      if (savedFiles) {
        return JSON.parse(savedFiles);
      }
    } catch (error) {
      console.error('Failed to parse saved files:', error);
    }
    return [];
  };
  
  const files = loadFiles();
  
  // Handle save settings
  const handleSaveSettings = async () => {
    setIsSaving(true);
    
    // Simulate saving settings
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setIsSaving(false);
    
    toast({
      title: 'Settings Saved',
      description: 'Your settings have been saved successfully.',
    });
  };
  
  // Handle prompt template selection
  const handleSelectTemplate = (template: string) => {
    // In a real implementation, this would apply the template to the prompt input
    console.log('Selected template:', template);
    
    toast({
      title: 'Template Selected',
      description: 'Template has been selected. Return to the main page to use it.',
    });
  };
  
  return (
    <div className="container mx-auto py-6 max-w-7xl">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button variant="ghost" size="sm" onClick={() => navigate('/')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Editor
          </Button>
          <h1 className="text-2xl font-bold ml-4">Advanced Settings</h1>
        </div>
        
        <Button onClick={handleSaveSettings} disabled={isSaving}>
          {isSaving ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Save Settings
            </>
          )}
        </Button>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="ai-models">AI Models</TabsTrigger>
          <TabsTrigger value="prompt-templates">Prompt Templates</TabsTrigger>
          <TabsTrigger value="project-structure">Project Structure</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
        </TabsList>
        
        <TabsContent value="ai-models">
          <div className="grid grid-cols-1 gap-6">
            <AIModelManager />
            
            <Card>
              <CardHeader>
                <CardTitle>Model Usage Statistics</CardTitle>
                <CardDescription>
                  Track your AI model usage and costs
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <p>No usage data available yet</p>
                  <p className="text-sm">Usage statistics will appear here as you use the AI models</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="prompt-templates">
          <div className="grid grid-cols-1 gap-6">
            <PromptTemplateManager onSelectTemplate={handleSelectTemplate} />
          </div>
        </TabsContent>
        
        <TabsContent value="project-structure">
          <div className="grid grid-cols-1 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Project Structure Visualization</CardTitle>
                <CardDescription>
                  Visualize your project structure and dependencies
                </CardDescription>
              </CardHeader>
              <CardContent className="h-[600px]">
                <ProjectStructureVisualizer files={files} />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="preferences">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Editor Preferences</CardTitle>
                <CardDescription>
                  Customize your code editor experience
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Theme</span>
                    <select className="p-2 border rounded">
                      <option>Light</option>
                      <option>Dark</option>
                      <option>System</option>
                    </select>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span>Font Size</span>
                    <select className="p-2 border rounded">
                      <option>12px</option>
                      <option>14px</option>
                      <option>16px</option>
                      <option>18px</option>
                    </select>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span>Tab Size</span>
                    <select className="p-2 border rounded">
                      <option>2 spaces</option>
                      <option>4 spaces</option>
                      <option>8 spaces</option>
                    </select>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span>Word Wrap</span>
                    <select className="p-2 border rounded">
                      <option>On</option>
                      <option>Off</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>AI Assistant Preferences</CardTitle>
                <CardDescription>
                  Customize how the AI assistant works
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Auto-Complete</span>
                    <select className="p-2 border rounded">
                      <option>Enabled</option>
                      <option>Disabled</option>
                    </select>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span>Code Suggestions</span>
                    <select className="p-2 border rounded">
                      <option>Aggressive</option>
                      <option>Moderate</option>
                      <option>Conservative</option>
                      <option>Disabled</option>
                    </select>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span>Auto-Save Generated Code</span>
                    <select className="p-2 border rounded">
                      <option>Always</option>
                      <option>Ask First</option>
                      <option>Never</option>
                    </select>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span>Context Window Size</span>
                    <select className="p-2 border rounded">
                      <option>Small (1000 tokens)</option>
                      <option>Medium (4000 tokens)</option>
                      <option>Large (8000 tokens)</option>
                      <option>Maximum (16000+ tokens)</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Keyboard Shortcuts</CardTitle>
                <CardDescription>
                  Customize keyboard shortcuts for common actions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h3 className="font-medium">Editor</h3>
                    <div className="space-y-1">
                      <div className="flex items-center justify-between text-sm">
                        <span>Save File</span>
                        <span className="font-mono bg-muted px-2 py-1 rounded">Ctrl+S</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>Find</span>
                        <span className="font-mono bg-muted px-2 py-1 rounded">Ctrl+F</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>Replace</span>
                        <span className="font-mono bg-muted px-2 py-1 rounded">Ctrl+H</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>Format Code</span>
                        <span className="font-mono bg-muted px-2 py-1 rounded">Shift+Alt+F</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="font-medium">AI Assistant</h3>
                    <div className="space-y-1">
                      <div className="flex items-center justify-between text-sm">
                        <span>Send Prompt</span>
                        <span className="font-mono bg-muted px-2 py-1 rounded">Ctrl+Enter</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>Apply Suggestion</span>
                        <span className="font-mono bg-muted px-2 py-1 rounded">Alt+Enter</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>Navigate History</span>
                        <span className="font-mono bg-muted px-2 py-1 rounded">Ctrl+↑/↓</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>Open Templates</span>
                        <span className="font-mono bg-muted px-2 py-1 rounded">Ctrl+Shift+T</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdvancedSettings;