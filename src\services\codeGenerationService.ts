import { FileNode } from "@/components/FileExplorer";

interface CodeGenerationOptions {
  language: string;
  framework?: string;
  style?: 'functional' | 'class-based' | 'procedural';
  includeComments: boolean;
  includeTests: boolean;
  includeTypes: boolean;
  targetFile?: string;
}

interface CodeGenerationResult {
  code: string;
  targetFile: string;
  language: string;
  dependencies?: string[];
  testCode?: string;
  testFile?: string;
}

interface CodeTemplate {
  name: string;
  description: string;
  language: string;
  framework?: string;
  template: string;
  defaultFileName: string;
}

/**
 * Service for generating code based on user prompts and project context
 */
export class CodeGenerationService {
  private templates: CodeTemplate[] = [
    {
      name: 'React Component',
      description: 'A functional React component with TypeScript',
      language: 'typescript',
      framework: 'react',
      template: `import React from 'react';

interface {{ComponentName}}Props {
  // Define your props here
}

const {{ComponentName}}: React.FC<{{ComponentName}}Props> = (props) => {
  return (
    <div>
      {/* Component content */}
    </div>
  );
};

export default {{ComponentName}};`,
      defaultFileName: 'Component.tsx'
    },
    {
      name: 'React Hook',
      description: 'A custom React hook with TypeScript',
      language: 'typescript',
      framework: 'react',
      template: `import { useState, useEffect } from 'react';

interface {{HookName}}Options {
  // Define your options here
}

export function {{hookName}}(options: {{HookName}}Options) {
  const [state, setState] = useState<any>(null);

  useEffect(() => {
    // Hook logic here
    return () => {
      // Cleanup logic
    };
  }, []);

  return {
    state,
    // Return values here
  };
}`,
      defaultFileName: 'useHook.ts'
    },
    {
      name: 'Express Route',
      description: 'An Express.js route handler',
      language: 'javascript',
      framework: 'express',
      template: `const express = require('express');
const router = express.Router();

/**
 * @route   GET /api/{{routeName}}
 * @desc    Get all {{routeName}}
 * @access  Public
 */
router.get('/', async (req, res) => {
  try {
    // Route logic here
    res.json({ message: 'Success' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route   POST /api/{{routeName}}
 * @desc    Create a new {{routeName}}
 * @access  Private
 */
router.post('/', async (req, res) => {
  try {
    // Route logic here
    res.json({ message: 'Created successfully' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;`,
      defaultFileName: 'route.js'
    },
    {
      name: 'Python Class',
      description: 'A Python class with methods',
      language: 'python',
      template: `class {{ClassName}}:
    """
    {{ClassName}} - Description of the class
    """
    
    def __init__(self):
        """
        Initialize the {{ClassName}} instance
        """
        pass
        
    def method_one(self, param1):
        """
        Description of method_one
        
        Args:
            param1: Description of param1
            
        Returns:
            Description of return value
        """
        pass
        
    def method_two(self, param1, param2=None):
        """
        Description of method_two
        
        Args:
            param1: Description of param1
            param2: Description of param2, defaults to None
            
        Returns:
            Description of return value
        """
        pass`,
      defaultFileName: 'class.py'
    }
  ];
  
  /**
   * Generate code based on user prompt and options
   */
  public async generateCode(
    prompt: string,
    options: CodeGenerationOptions,
    projectFiles: FileNode[]
  ): Promise<CodeGenerationResult> {
    // In a real implementation, this would call an AI model
    // For now, we'll use templates and simple replacements
    
    // Find a suitable template based on options
    const template = this.findTemplate(options.language, options.framework);
    
    if (!template) {
      throw new Error(`No template found for language: ${options.language}, framework: ${options.framework || 'any'}`);
    }
    
    // Extract key entities from the prompt
    const entities = this.extractEntitiesFromPrompt(prompt);
    
    // Apply the template with extracted entities
    let code = this.applyTemplate(template.template, entities);
    
    // Add comments if requested
    if (options.includeComments) {
      code = this.addComments(code, options.language);
    }
    
    // Generate file name
    const fileName = options.targetFile || this.generateFileName(template, entities);
    
    // Generate test code if requested
    let testCode;
    let testFile;
    
    if (options.includeTests) {
      const testResult = this.generateTestCode(code, fileName, options.language);
      testCode = testResult.code;
      testFile = testResult.fileName;
    }
    
    return {
      code,
      targetFile: fileName,
      language: options.language,
      testCode,
      testFile
    };
  }
  
  /**
   * Refactor existing code based on user prompt
   */
  public async refactorCode(
    sourceCode: string,
    prompt: string,
    options: {
      language: string;
      improvePerformance?: boolean;
      improveReadability?: boolean;
      modernizeSyntax?: boolean;
    }
  ): Promise<string> {
    // In a real implementation, this would call an AI model
    // For now, we'll do some simple transformations
    
    let refactoredCode = sourceCode;
    
    if (options.improveReadability) {
      refactoredCode = this.improveReadability(refactoredCode, options.language);
    }
    
    if (options.modernizeSyntax) {
      refactoredCode = this.modernizeSyntax(refactoredCode, options.language);
    }
    
    if (options.improvePerformance) {
      refactoredCode = this.improvePerformance(refactoredCode, options.language);
    }
    
    return refactoredCode;
  }
  
  /**
   * Generate documentation for existing code
   */
  public async generateDocumentation(
    sourceCode: string,
    language: string,
    format: 'jsdoc' | 'markdown' | 'docstring' = 'jsdoc'
  ): Promise<string> {
    // In a real implementation, this would call an AI model
    // For now, we'll return a simple template
    
    switch (format) {
      case 'jsdoc':
        return this.generateJSDocDocumentation(sourceCode);
      case 'markdown':
        return this.generateMarkdownDocumentation(sourceCode, language);
      case 'docstring':
        return this.generateDocstringDocumentation(sourceCode);
      default:
        throw new Error(`Unsupported documentation format: ${format}`);
    }
  }
  
  /**
   * Find a template based on language and framework
   */
  private findTemplate(language: string, framework?: string): CodeTemplate | undefined {
    if (framework) {
      // Try to find a template matching both language and framework
      const exactMatch = this.templates.find(
        t => t.language === language && t.framework === framework
      );
      
      if (exactMatch) return exactMatch;
    }
    
    // Fall back to just matching the language
    return this.templates.find(t => t.language === language);
  }
  
  /**
   * Extract entities from the prompt using simple heuristics
   */
  private extractEntitiesFromPrompt(prompt: string): Record<string, string> {
    const entities: Record<string, string> = {};
    
    // Extract component name (capitalized words)
    const componentNameMatch = prompt.match(/\b([A-Z][a-zA-Z]*(?:\s+[A-Z][a-zA-Z]*)*)\b/);
    if (componentNameMatch) {
      entities['ComponentName'] = componentNameMatch[1].replace(/\s+/g, '');
    } else {
      entities['ComponentName'] = 'MyComponent';
    }
    
    // Extract hook name (use* followed by capitalized word)
    const hookNameMatch = prompt.match(/\buse([A-Z][a-zA-Z]*)\b/);
    if (hookNameMatch) {
      entities['HookName'] = hookNameMatch[1];
      entities['hookName'] = `use${hookNameMatch[1]}`;
    } else {
      entities['HookName'] = 'Hook';
      entities['hookName'] = 'useHook';
    }
    
    // Extract class name (class followed by capitalized word)
    const classNameMatch = prompt.match(/\bclass\s+([A-Z][a-zA-Z]*)\b/i);
    if (classNameMatch) {
      entities['ClassName'] = classNameMatch[1];
    } else {
      entities['ClassName'] = 'MyClass';
    }
    
    // Extract route name (lowercase words)
    const routeNameMatch = prompt.match(/\broute\s+for\s+([a-z][a-zA-Z]*)\b/i);
    if (routeNameMatch) {
      entities['routeName'] = routeNameMatch[1].toLowerCase();
    } else {
      entities['routeName'] = 'items';
    }
    
    return entities;
  }
  
  /**
   * Apply a template with the given entities
   */
  private applyTemplate(template: string, entities: Record<string, string>): string {
    let result = template;
    
    // Replace all {{Entity}} placeholders with their values
    for (const [key, value] of Object.entries(entities)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, value);
    }
    
    return result;
  }
  
  /**
   * Add comments to the code based on language
   */
  private addComments(code: string, language: string): string {
    switch (language) {
      case 'javascript':
      case 'typescript':
        return this.addJavaScriptComments(code);
      case 'python':
        return this.addPythonComments(code);
      default:
        return code;
    }
  }
  
  /**
   * Add JavaScript/TypeScript comments
   */
  private addJavaScriptComments(code: string): string {
    // Add a file header comment
    let commented = `/**
 * @file This file was generated by AI Code Scribe
 * @description Generated code based on user requirements
 */\n\n${code}`;
    
    // Add comments to functions that don't already have them
    commented = commented.replace(
      /(\b(?:function|const|let|var)\s+([a-zA-Z0-9_$]+)\s*=?\s*(?:function)?\s*\([^)]*\))\s*{/g,
      (match, declaration, funcName) => {
        // Check if there's already a comment before this function
        const prevLines = commented.substring(0, commented.indexOf(match)).split('\n').slice(-3);
        if (prevLines.some(line => line.trim().startsWith('/*') || line.trim().startsWith('//'))) {
          return match + '{';
        }
        
        return `/**
 * ${funcName} - Description
 * @param {*} params - Parameters description
 * @returns {*} Return value description
 */
${declaration} {`;
      }
    );
    
    return commented;
  }
  
  /**
   * Add Python comments
   */
  private addPythonComments(code: string): string {
    // Add a file header comment
    let commented = `"""
This file was generated by AI Code Scribe
Description: Generated code based on user requirements
"""\n\n${code}`;
    
    // Add docstrings to functions that don't already have them
    commented = commented.replace(
      /(\bdef\s+([a-zA-Z0-9_$]+)\s*\([^)]*\))\s*:/g,
      (match, declaration, funcName) => {
        // Check if there's already a docstring after this function
        const nextLines = commented.substring(commented.indexOf(match) + match.length).split('\n').slice(0, 3);
        if (nextLines.some(line => line.trim().startsWith('"""') || line.trim().startsWith("'''"))) {
          return match + ':';
        }
        
        return `${declaration}:
    """
    ${funcName} - Description
    
    Args:
        params: Parameters description
        
    Returns:
        Return value description
    """`;
      }
    );
    
    return commented;
  }
  
  /**
   * Generate a file name based on the template and entities
   */
  private generateFileName(template: CodeTemplate, entities: Record<string, string>): string {
    // Replace placeholders in the default file name
    let fileName = template.defaultFileName;
    
    for (const [key, value] of Object.entries(entities)) {
      if (fileName.includes(key)) {
        fileName = fileName.replace(key, value);
      }
    }
    
    // If the file name still has a generic component/class name, use the entity value
    if (fileName.startsWith('Component') && entities['ComponentName']) {
      fileName = `${entities['ComponentName']}.tsx`;
    } else if (fileName.startsWith('use') && entities['hookName']) {
      fileName = `${entities['hookName']}.ts`;
    } else if (fileName.startsWith('class') && entities['ClassName']) {
      fileName = `${entities['ClassName'].toLowerCase()}.py`;
    } else if (fileName.startsWith('route') && entities['routeName']) {
      fileName = `${entities['routeName']}Routes.js`;
    }
    
    return fileName;
  }
  
  /**
   * Generate test code for the given source code
   */
  private generateTestCode(
    sourceCode: string,
    fileName: string,
    language: string
  ): { code: string; fileName: string } {
    // Generate a test file name
    const testFileName = this.generateTestFileName(fileName);
    
    // Generate test code based on language
    let testCode = '';
    
    switch (language) {
      case 'javascript':
      case 'typescript':
        testCode = this.generateJavaScriptTest(sourceCode, fileName);
        break;
      case 'python':
        testCode = this.generatePythonTest(sourceCode, fileName);
        break;
      default:
        testCode = `// Test code for ${fileName}\n// Not implemented for ${language}`;
    }
    
    return { code: testCode, fileName: testFileName };
  }
  
  /**
   * Generate a test file name based on the source file name
   */
  private generateTestFileName(fileName: string): string {
    const parts = fileName.split('.');
    const extension = parts.pop();
    const baseName = parts.join('.');
    
    if (extension === 'ts' || extension === 'tsx') {
      return `${baseName}.test.${extension}`;
    } else if (extension === 'js' || extension === 'jsx') {
      return `${baseName}.test.js`;
    } else if (extension === 'py') {
      return `test_${baseName}.py`;
    } else {
      return `${baseName}.test.${extension}`;
    }
  }
  
  /**
   * Generate JavaScript/TypeScript test code
   */
  private generateJavaScriptTest(sourceCode: string, fileName: string): string {
    // Extract the component/function name from the source code
    const componentMatch = sourceCode.match(/(?:function|const|class)\s+([A-Z][a-zA-Z0-9_$]*)/);
    const componentName = componentMatch ? componentMatch[1] : 'Component';
    
    // Check if it's a React component
    const isReactComponent = sourceCode.includes('import React') || sourceCode.includes('from "react"');
    
    if (isReactComponent) {
      return `import React from 'react';
import { render, screen } from '@testing-library/react';
import ${componentName} from './${fileName.replace(/\.(tsx|jsx|js|ts)$/, '')}';

describe('${componentName}', () => {
  test('renders correctly', () => {
    render(<${componentName} />);
    // Add your assertions here
    expect(screen.getByText(/some text/i)).toBeInTheDocument();
  });
  
  test('handles interactions', () => {
    render(<${componentName} />);
    // Add your interaction tests here
  });
});`;
    } else {
      return `import ${componentName} from './${fileName.replace(/\.(tsx|jsx|js|ts)$/, '')}';

describe('${componentName}', () => {
  test('works correctly', () => {
    // Add your test implementation here
    expect(${componentName}()).toBeDefined();
  });
  
  test('handles edge cases', () => {
    // Add your edge case tests here
  });
});`;
    }
  }
  
  /**
   * Generate Python test code
   */
  private generatePythonTest(sourceCode: string, fileName: string): string {
    // Extract the class name from the source code
    const classMatch = sourceCode.match(/class\s+([A-Z][a-zA-Z0-9_]*)/);
    const className = classMatch ? classMatch[1] : 'MyClass';
    
    return `import unittest
from ${fileName.replace('.py', '')} import ${className}

class Test${className}(unittest.TestCase):
    def setUp(self):
        self.instance = ${className}()
        
    def test_initialization(self):
        # Test that the instance initializes correctly
        self.assertIsInstance(self.instance, ${className})
        
    def test_methods(self):
        # Test the methods of the class
        # Add your assertions here
        pass
        
if __name__ == '__main__':
    unittest.main()`;
  }
  
  /**
   * Improve code readability
   */
  private improveReadability(code: string, language: string): string {
    // This is a simplified implementation
    // In a real system, this would use more sophisticated techniques
    
    switch (language) {
      case 'javascript':
      case 'typescript':
        // Add spaces around operators
        code = code.replace(/([+\-*/%=<>!&|^])([\w\d])/g, '$1 $2');
        code = code.replace(/([\w\d])([+\-*/%=<>!&|^])/g, '$1 $2');
        
        // Add newlines after blocks
        code = code.replace(/}(\w)/g, '}\n\n$1');
        
        // Add newlines before comments
        code = code.replace(/([^\/])(\/\/[^\n]*)/g, '$1\n$2');
        
        break;
        
      case 'python':
        // Add spaces around operators
        code = code.replace(/([+\-*/%=<>!&|^])([\w\d])/g, '$1 $2');
        code = code.replace(/([\w\d])([+\-*/%=<>!&|^])/g, '$1 $2');
        
        // Add newlines between functions
        code = code.replace(/(\bdef\s+[a-zA-Z0-9_]+\s*\([^)]*\):)/g, '\n$1');
        
        break;
    }
    
    return code;
  }
  
  /**
   * Modernize code syntax
   */
  private modernizeSyntax(code: string, language: string): string {
    // This is a simplified implementation
    // In a real system, this would use more sophisticated techniques
    
    switch (language) {
      case 'javascript':
      case 'typescript':
        // Convert var to const/let
        code = code.replace(/\bvar\s+([a-zA-Z0-9_$]+)\s*=/g, 'const $1 =');
        
        // Convert function declarations to arrow functions
        code = code.replace(/function\s+([a-zA-Z0-9_$]+)\s*\(([^)]*)\)\s*{/g, 'const $1 = ($2) => {');
        
        // Convert old-style string concatenation to template literals
        code = code.replace(/(['"])([^'"]*)\1\s*\+\s*([a-zA-Z0-9_$]+)\s*\+\s*(['"])([^'"]*)\4/g, '`$2${$3}$5`');
        
        break;
        
      case 'python':
        // Convert old-style string formatting to f-strings
        code = code.replace(/(['"])([^'"]*){([^}]+)}([^'"]*)\1\.format\(([^)]+)\)/g, 'f\'$2{$5}$4\'');
        
        // Convert old-style exception handling
        code = code.replace(/except\s+([a-zA-Z0-9_]+),\s+([a-zA-Z0-9_]+):/g, 'except $1 as $2:');
        
        break;
    }
    
    return code;
  }
  
  /**
   * Improve code performance
   */
  private improvePerformance(code: string, language: string): string {
    // This is a simplified implementation
    // In a real system, this would use more sophisticated techniques
    
    switch (language) {
      case 'javascript':
      case 'typescript':
        // Replace array.push in loops with direct assignment
        code = code.replace(/for\s*\(\s*let\s+([a-zA-Z0-9_$]+)\s*=\s*0\s*;\s*\1\s*<\s*([a-zA-Z0-9_$]+)\.length\s*;\s*\1\+\+\s*\)\s*{\s*([a-zA-Z0-9_$]+)\.push\(([^;]+)\);\s*}/g, 
          'const $3 = Array($2.length);\nfor (let $1 = 0; $1 < $2.length; $1++) {\n  $3[$1] = $4;\n}');
        
        break;
        
      case 'python':
        // Replace list appends in loops with list comprehensions
        code = code.replace(/([a-zA-Z0-9_]+)\s*=\s*\[\]\s*\n\s*for\s+([a-zA-Z0-9_]+)\s+in\s+([a-zA-Z0-9_]+):\s*\n\s*\1\.append\(([^)]+)\)/g, 
          '$1 = [$4 for $2 in $3]');
        
        break;
    }
    
    return code;
  }
  
  /**
   * Generate JSDoc documentation
   */
  private generateJSDocDocumentation(sourceCode: string): string {
    // Extract function and class names
    const functionMatches = Array.from(sourceCode.matchAll(/(?:function|const|let|var)\s+([a-zA-Z0-9_$]+)\s*=?\s*(?:function)?\s*\(([^)]*)\)/g));
    const classMatches = Array.from(sourceCode.matchAll(/class\s+([a-zA-Z0-9_$]+)(?:\s+extends\s+([a-zA-Z0-9_$]+))?\s*{/g));
    
    let documentation = '# API Documentation\n\n';
    
    // Document classes
    if (classMatches.length > 0) {
      documentation += '## Classes\n\n';
      
      for (const match of classMatches) {
        const className = match[1];
        const parentClass = match[2];
        
        documentation += `### ${className}\n\n`;
        
        if (parentClass) {
          documentation += `Extends: \`${parentClass}\`\n\n`;
        }
        
        documentation += 'Description of the class.\n\n';
        
        // Find methods of this class
        const methodRegex = new RegExp(`(?:async\\s+)?([a-zA-Z0-9_$]+)\\s*\\(([^)]*)\\)\\s*{`, 'g');
        const classBody = sourceCode.substring(match.index);
        const methodMatches = Array.from(classBody.matchAll(methodRegex));
        
        if (methodMatches.length > 0) {
          documentation += '#### Methods\n\n';
          
          for (const methodMatch of methodMatches) {
            const methodName = methodMatch[1];
            const params = methodMatch[2].split(',').map(p => p.trim()).filter(Boolean);
            
            documentation += `##### \`${methodName}(${params.join(', ')})\`\n\n`;
            documentation += 'Description of the method.\n\n';
            
            if (params.length > 0) {
              documentation += 'Parameters:\n\n';
              
              for (const param of params) {
                documentation += `- \`${param}\`: Description of the parameter\n`;
              }
              
              documentation += '\n';
            }
            
            documentation += 'Returns: Description of the return value\n\n';
          }
        }
      }
    }
    
    // Document functions
    if (functionMatches.length > 0) {
      documentation += '## Functions\n\n';
      
      for (const match of functionMatches) {
        const functionName = match[1];
        const params = match[2].split(',').map(p => p.trim()).filter(Boolean);
        
        documentation += `### \`${functionName}(${params.join(', ')})\`\n\n`;
        documentation += 'Description of the function.\n\n';
        
        if (params.length > 0) {
          documentation += 'Parameters:\n\n';
          
          for (const param of params) {
            documentation += `- \`${param}\`: Description of the parameter\n`;
          }
          
          documentation += '\n';
        }
        
        documentation += 'Returns: Description of the return value\n\n';
      }
    }
    
    return documentation;
  }
  
  /**
   * Generate Markdown documentation
   */
  private generateMarkdownDocumentation(sourceCode: string, language: string): string {
    // For simplicity, we'll reuse the JSDoc documentation generator
    return this.generateJSDocDocumentation(sourceCode);
  }
  
  /**
   * Generate Python docstring documentation
   */
  private generateDocstringDocumentation(sourceCode: string): string {
    // Extract function and class names
    const functionMatches = Array.from(sourceCode.matchAll(/def\s+([a-zA-Z0-9_]+)\s*\(([^)]*)\)/g));
    const classMatches = Array.from(sourceCode.matchAll(/class\s+([a-zA-Z0-9_]+)(?:\s*\(([^)]*)\))?\s*:/g));
    
    let documentation = '# API Documentation\n\n';
    
    // Document classes
    if (classMatches.length > 0) {
      documentation += '## Classes\n\n';
      
      for (const match of classMatches) {
        const className = match[1];
        const parentClasses = match[2];
        
        documentation += `### ${className}\n\n`;
        
        if (parentClasses) {
          documentation += `Inherits from: \`${parentClasses}\`\n\n`;
        }
        
        documentation += 'Description of the class.\n\n';
        
        // Find methods of this class
        const methodRegex = new RegExp(`def\\s+([a-zA-Z0-9_]+)\\s*\\(([^)]*)\\)`, 'g');
        const classBody = sourceCode.substring(match.index);
        const methodMatches = Array.from(classBody.matchAll(methodRegex));
        
        if (methodMatches.length > 0) {
          documentation += '#### Methods\n\n';
          
          for (const methodMatch of methodMatches) {
            const methodName = methodMatch[1];
            const params = methodMatch[2].split(',').map(p => p.trim()).filter(Boolean);
            
            documentation += `##### \`${methodName}(${params.join(', ')})\`\n\n`;
            documentation += 'Description of the method.\n\n';
            
            if (params.length > 0) {
              documentation += 'Parameters:\n\n';
              
              for (const param of params) {
                documentation += `- \`${param}\`: Description of the parameter\n`;
              }
              
              documentation += '\n';
            }
            
            documentation += 'Returns: Description of the return value\n\n';
          }
        }
      }
    }
    
    // Document functions
    if (functionMatches.length > 0) {
      documentation += '## Functions\n\n';
      
      for (const match of functionMatches) {
        const functionName = match[1];
        const params = match[2].split(',').map(p => p.trim()).filter(Boolean);
        
        documentation += `### \`${functionName}(${params.join(', ')})\`\n\n`;
        documentation += 'Description of the function.\n\n';
        
        if (params.length > 0) {
          documentation += 'Parameters:\n\n';
          
          for (const param of params) {
            documentation += `- \`${param}\`: Description of the parameter\n`;
          }
          
          documentation += '\n';
        }
        
        documentation += 'Returns: Description of the return value\n\n';
      }
    }
    
    return documentation;
  }
}

// Export a singleton instance
export const codeGenerationService = new CodeGenerationService();