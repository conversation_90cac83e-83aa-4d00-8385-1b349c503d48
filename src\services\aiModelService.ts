/**
 * AI Model Service
 * 
 * This service provides a unified interface for interacting with different AI models
 * including Ollama, OpenAI, Anthropic, and others.
 */

export interface AIModelConfig {
  id: string;
  name: string;
  provider: 'ollama' | 'openai' | 'anthropic' | 'custom';
  apiEndpoint: string;
  apiKey?: string;
  modelName: string;
  contextLength: number;
  supportsStreaming: boolean;
  supportsVision: boolean;
  supportsFunctions: boolean;
  defaultTemperature: number;
  defaultTopP: number;
  defaultTopK: number;
  maxTokens: number;
  costPer1KTokens?: number;
  isEnabled: boolean;
}

export interface AIModelResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
    estimatedCost?: number;
  };
  model: string;
  finishReason?: 'stop' | 'length' | 'content_filter' | 'function_call' | 'error';
  error?: string;
}

export interface AIModelStreamChunk {
  content: string;
  isComplete: boolean;
  error?: string;
}

export interface AIModelRequestOptions {
  temperature?: number;
  topP?: number;
  topK?: number;
  maxTokens?: number;
  stopSequences?: string[];
  stream?: boolean;
  functions?: AIModelFunction[];
}

export interface AIModelFunction {
  name: string;
  description: string;
  parameters: Record<string, any>;
  required?: string[];
}

export interface AIModelFunctionCall {
  name: string;
  arguments: Record<string, any>;
}

export class AIModelService {
  private models: AIModelConfig[] = [];
  private activeModelId: string | null = null;
  
  constructor() {
    this.loadModels();
  }
  
  /**
   * Load models from localStorage or initialize with defaults
   */
  private loadModels() {
    try {
      const savedModels = localStorage.getItem('ai-models');
      if (savedModels) {
        this.models = JSON.parse(savedModels);
      } else {
        // Initialize with default models
        this.models = [
          {
            id: 'ollama-qwen',
            name: 'Qwen2.5 Coder (Ollama)',
            provider: 'ollama',
            apiEndpoint: '/ollama-api/api/generate',
            modelName: 'qwen2.5-coder:7b',
            contextLength: 8192,
            supportsStreaming: true,
            supportsVision: false,
            supportsFunctions: false,
            defaultTemperature: 0.7,
            defaultTopP: 0.9,
            defaultTopK: 40,
            maxTokens: 2048,
            isEnabled: true
          },
          {
            id: 'ollama-llama',
            name: 'Llama 3 (Ollama)',
            provider: 'ollama',
            apiEndpoint: '/ollama-api/api/generate',
            modelName: 'llama3:8b',
            contextLength: 8192,
            supportsStreaming: true,
            supportsVision: false,
            supportsFunctions: false,
            defaultTemperature: 0.7,
            defaultTopP: 0.9,
            defaultTopK: 40,
            maxTokens: 2048,
            isEnabled: true
          },
          {
            id: 'openai-gpt4',
            name: 'GPT-4 Turbo (OpenAI)',
            provider: 'openai',
            apiEndpoint: 'https://api.openai.com/v1/chat/completions',
            apiKey: '',
            modelName: 'gpt-4-turbo',
            contextLength: 128000,
            supportsStreaming: true,
            supportsVision: true,
            supportsFunctions: true,
            defaultTemperature: 0.7,
            defaultTopP: 1.0,
            defaultTopK: 0,
            maxTokens: 4096,
            costPer1KTokens: 0.01,
            isEnabled: false
          },
          {
            id: 'anthropic-claude',
            name: 'Claude 3 Opus (Anthropic)',
            provider: 'anthropic',
            apiEndpoint: 'https://api.anthropic.com/v1/messages',
            apiKey: '',
            modelName: 'claude-3-opus-20240229',
            contextLength: 200000,
            supportsStreaming: true,
            supportsVision: true,
            supportsFunctions: true,
            defaultTemperature: 0.7,
            defaultTopP: 1.0,
            defaultTopK: 0,
            maxTokens: 4096,
            costPer1KTokens: 0.015,
            isEnabled: false
          }
        ];
        this.saveModels();
      }
      
      // Set active model to the first enabled model
      const enabledModels = this.models.filter(model => model.isEnabled);
      if (enabledModels.length > 0) {
        this.activeModelId = enabledModels[0].id;
      }
    } catch (error) {
      console.error('Error loading AI models:', error);
      // Initialize with a default Ollama model
      this.models = [
        {
          id: 'ollama-default',
          name: 'Default Ollama Model',
          provider: 'ollama',
          apiEndpoint: '/ollama-api/api/generate',
          modelName: 'qwen2.5-coder:7b',
          contextLength: 8192,
          supportsStreaming: true,
          supportsVision: false,
          supportsFunctions: false,
          defaultTemperature: 0.7,
          defaultTopP: 0.9,
          defaultTopK: 40,
          maxTokens: 2048,
          isEnabled: true
        }
      ];
      this.activeModelId = 'ollama-default';
      this.saveModels();
    }
  }
  
  /**
   * Save models to localStorage
   */
  private saveModels() {
    try {
      localStorage.setItem('ai-models', JSON.stringify(this.models));
    } catch (error) {
      console.error('Error saving AI models:', error);
    }
  }
  
  /**
   * Get all available models
   */
  public getModels(): AIModelConfig[] {
    return this.models;
  }
  
  /**
   * Get the active model
   */
  public getActiveModel(): AIModelConfig | null {
    if (!this.activeModelId) return null;
    return this.models.find(model => model.id === this.activeModelId) || null;
  }
  
  /**
   * Set the active model
   */
  public setActiveModel(modelId: string): boolean {
    const model = this.models.find(m => m.id === modelId);
    if (model && model.isEnabled) {
      this.activeModelId = modelId;
      return true;
    }
    return false;
  }
  
  /**
   * Add a new model
   */
  public addModel(model: Omit<AIModelConfig, 'id'>): string {
    const id = `model-${Date.now()}`;
    const newModel: AIModelConfig = {
      ...model,
      id
    };
    
    this.models.push(newModel);
    this.saveModels();
    
    return id;
  }
  
  /**
   * Update an existing model
   */
  public updateModel(id: string, updates: Partial<AIModelConfig>): boolean {
    const index = this.models.findIndex(model => model.id === id);
    if (index === -1) return false;
    
    this.models[index] = {
      ...this.models[index],
      ...updates
    };
    
    this.saveModels();
    return true;
  }
  
  /**
   * Delete a model
   */
  public deleteModel(id: string): boolean {
    const index = this.models.findIndex(model => model.id === id);
    if (index === -1) return false;
    
    this.models.splice(index, 1);
    
    // If the active model was deleted, set a new active model
    if (this.activeModelId === id) {
      const enabledModels = this.models.filter(model => model.isEnabled);
      this.activeModelId = enabledModels.length > 0 ? enabledModels[0].id : null;
    }
    
    this.saveModels();
    return true;
  }
  
  /**
   * Send a request to the AI model
   */
  public async sendRequest(
    prompt: string,
    options: AIModelRequestOptions = {}
  ): Promise<AIModelResponse> {
    const activeModel = this.getActiveModel();
    if (!activeModel) {
      throw new Error('No active AI model selected');
    }
    
    try {
      switch (activeModel.provider) {
        case 'ollama':
          return this.sendOllamaRequest(activeModel, prompt, options);
        case 'openai':
          return this.sendOpenAIRequest(activeModel, prompt, options);
        case 'anthropic':
          return this.sendAnthropicRequest(activeModel, prompt, options);
        case 'custom':
          return this.sendCustomRequest(activeModel, prompt, options);
        default:
          throw new Error(`Unsupported provider: ${activeModel.provider}`);
      }
    } catch (error) {
      console.error(`Error sending request to ${activeModel.name}:`, error);
      return {
        content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        model: activeModel.modelName,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Send a streaming request to the AI model
   */
  public async *sendStreamingRequest(
    prompt: string,
    options: AIModelRequestOptions = {}
  ): AsyncGenerator<AIModelStreamChunk> {
    const activeModel = this.getActiveModel();
    if (!activeModel) {
      yield {
        content: 'Error: No active AI model selected',
        isComplete: true,
        error: 'No active AI model selected'
      };
      return;
    }
    
    if (!activeModel.supportsStreaming) {
      // Fall back to non-streaming request
      try {
        const response = await this.sendRequest(prompt, options);
        yield {
          content: response.content,
          isComplete: true,
          error: response.error
        };
      } catch (error) {
        yield {
          content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          isComplete: true,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
      return;
    }
    
    try {
      switch (activeModel.provider) {
        case 'ollama':
          yield* this.sendOllamaStreamingRequest(activeModel, prompt, options);
          break;
        case 'openai':
          yield* this.sendOpenAIStreamingRequest(activeModel, prompt, options);
          break;
        case 'anthropic':
          yield* this.sendAnthropicStreamingRequest(activeModel, prompt, options);
          break;
        case 'custom':
          yield* this.sendCustomStreamingRequest(activeModel, prompt, options);
          break;
        default:
          throw new Error(`Unsupported provider: ${activeModel.provider}`);
      }
    } catch (error) {
      yield {
        content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        isComplete: true,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Send a request to Ollama
   */
  private async sendOllamaRequest(
    model: AIModelConfig,
    prompt: string,
    options: AIModelRequestOptions
  ): Promise<AIModelResponse> {
    const temperature = options.temperature ?? model.defaultTemperature;
    const topP = options.topP ?? model.defaultTopP;
    const topK = options.topK ?? model.defaultTopK;
    const maxTokens = options.maxTokens ?? model.maxTokens;
    
    const response = await fetch(model.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: model.modelName,
        prompt,
        stream: false,
        options: {
          temperature,
          top_p: topP,
          top_k: topK,
          num_predict: maxTokens
        }
      }),
    });
    
    if (!response.ok) {
      throw new Error(`Ollama API error: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    
    // Extract content based on response format
    let content = '';
    if (data.message && typeof data.message.content === 'string') {
      content = data.message.content;
    } else if (typeof data.response === 'string') {
      content = data.response;
    } else if (typeof data.content === 'string') {
      content = data.content;
    } else {
      content = JSON.stringify(data);
    }
    
    return {
      content,
      model: model.modelName,
      usage: {
        promptTokens: data.prompt_eval_count || 0,
        completionTokens: data.eval_count || 0,
        totalTokens: (data.prompt_eval_count || 0) + (data.eval_count || 0)
      }
    };
  }
  
  /**
   * Send a streaming request to Ollama
   */
  private async *sendOllamaStreamingRequest(
    model: AIModelConfig,
    prompt: string,
    options: AIModelRequestOptions
  ): AsyncGenerator<AIModelStreamChunk> {
    const temperature = options.temperature ?? model.defaultTemperature;
    const topP = options.topP ?? model.defaultTopP;
    const topK = options.topK ?? model.defaultTopK;
    const maxTokens = options.maxTokens ?? model.maxTokens;
    
    const response = await fetch(model.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: model.modelName,
        prompt,
        stream: true,
        options: {
          temperature,
          top_p: topP,
          top_k: topK,
          num_predict: maxTokens
        }
      }),
    });
    
    if (!response.ok) {
      throw new Error(`Ollama API error: ${response.status} ${response.statusText}`);
    }
    
    if (!response.body) {
      throw new Error('Response body is null');
    }
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let done = false;
    
    while (!done) {
      const { value, done: readerDone } = await reader.read();
      done = readerDone;
      
      if (done) {
        yield { content: '', isComplete: true };
        break;
      }
      
      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split('\n').filter(line => line.trim() !== '');
      
      for (const line of lines) {
        try {
          const data = JSON.parse(line);
          
          // Extract content based on response format
          let content = '';
          if (data.message && typeof data.message.content === 'string') {
            content = data.message.content;
          } else if (typeof data.response === 'string') {
            content = data.response;
          } else if (typeof data.content === 'string') {
            content = data.content;
          }
          
          yield {
            content,
            isComplete: data.done || false
          };
        } catch (error) {
          console.error('Error parsing Ollama stream chunk:', error);
        }
      }
    }
  }
  
  /**
   * Send a request to OpenAI
   */
  private async sendOpenAIRequest(
    model: AIModelConfig,
    prompt: string,
    options: AIModelRequestOptions
  ): Promise<AIModelResponse> {
    if (!model.apiKey) {
      throw new Error('OpenAI API key is required');
    }
    
    const temperature = options.temperature ?? model.defaultTemperature;
    const topP = options.topP ?? model.defaultTopP;
    const maxTokens = options.maxTokens ?? model.maxTokens;
    
    const requestBody: any = {
      model: model.modelName,
      messages: [
        { role: 'user', content: prompt }
      ],
      temperature,
      top_p: topP,
      max_tokens: maxTokens,
      stream: false
    };
    
    if (options.stopSequences && options.stopSequences.length > 0) {
      requestBody.stop = options.stopSequences;
    }
    
    if (options.functions && options.functions.length > 0 && model.supportsFunctions) {
      requestBody.functions = options.functions;
    }
    
    const response = await fetch(model.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${model.apiKey}`
      },
      body: JSON.stringify(requestBody),
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
    }
    
    const data = await response.json();
    
    const content = data.choices[0]?.message?.content || '';
    const finishReason = data.choices[0]?.finish_reason;
    
    let functionCall = null;
    if (data.choices[0]?.message?.function_call) {
      functionCall = {
        name: data.choices[0].message.function_call.name,
        arguments: JSON.parse(data.choices[0].message.function_call.arguments)
      };
    }
    
    return {
      content,
      model: data.model || model.modelName,
      finishReason,
      usage: data.usage ? {
        promptTokens: data.usage.prompt_tokens,
        completionTokens: data.usage.completion_tokens,
        totalTokens: data.usage.total_tokens,
        estimatedCost: data.usage.total_tokens * (model.costPer1KTokens || 0) / 1000
      } : undefined
    };
  }
  
  /**
   * Send a streaming request to OpenAI
   */
  private async *sendOpenAIStreamingRequest(
    model: AIModelConfig,
    prompt: string,
    options: AIModelRequestOptions
  ): AsyncGenerator<AIModelStreamChunk> {
    if (!model.apiKey) {
      throw new Error('OpenAI API key is required');
    }
    
    const temperature = options.temperature ?? model.defaultTemperature;
    const topP = options.topP ?? model.defaultTopP;
    const maxTokens = options.maxTokens ?? model.maxTokens;
    
    const requestBody: any = {
      model: model.modelName,
      messages: [
        { role: 'user', content: prompt }
      ],
      temperature,
      top_p: topP,
      max_tokens: maxTokens,
      stream: true
    };
    
    if (options.stopSequences && options.stopSequences.length > 0) {
      requestBody.stop = options.stopSequences;
    }
    
    if (options.functions && options.functions.length > 0 && model.supportsFunctions) {
      requestBody.functions = options.functions;
    }
    
    const response = await fetch(model.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${model.apiKey}`
      },
      body: JSON.stringify(requestBody),
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
    }
    
    if (!response.body) {
      throw new Error('Response body is null');
    }
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let done = false;
    
    while (!done) {
      const { value, done: readerDone } = await reader.read();
      done = readerDone;
      
      if (done) {
        yield { content: '', isComplete: true };
        break;
      }
      
      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split('\n').filter(line => line.trim() !== '');
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          
          if (data === '[DONE]') {
            yield { content: '', isComplete: true };
            continue;
          }
          
          try {
            const parsed = JSON.parse(data);
            const content = parsed.choices[0]?.delta?.content || '';
            const isComplete = parsed.choices[0]?.finish_reason != null;
            
            yield { content, isComplete };
          } catch (error) {
            console.error('Error parsing OpenAI stream chunk:', error);
          }
        }
      }
    }
  }
  
  /**
   * Send a request to Anthropic
   */
  private async sendAnthropicRequest(
    model: AIModelConfig,
    prompt: string,
    options: AIModelRequestOptions
  ): Promise<AIModelResponse> {
    if (!model.apiKey) {
      throw new Error('Anthropic API key is required');
    }
    
    const temperature = options.temperature ?? model.defaultTemperature;
    const topP = options.topP ?? model.defaultTopP;
    const maxTokens = options.maxTokens ?? model.maxTokens;
    
    const requestBody: any = {
      model: model.modelName,
      messages: [
        { role: 'user', content: prompt }
      ],
      temperature,
      top_p: topP,
      max_tokens: maxTokens,
      stream: false
    };
    
    if (options.stopSequences && options.stopSequences.length > 0) {
      requestBody.stop_sequences = options.stopSequences;
    }
    
    const response = await fetch(model.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': model.apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody),
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Anthropic API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
    }
    
    const data = await response.json();
    
    const content = data.content[0]?.text || '';
    
    return {
      content,
      model: data.model || model.modelName,
      usage: data.usage ? {
        promptTokens: data.usage.input_tokens,
        completionTokens: data.usage.output_tokens,
        totalTokens: data.usage.input_tokens + data.usage.output_tokens,
        estimatedCost: (data.usage.input_tokens + data.usage.output_tokens) * (model.costPer1KTokens || 0) / 1000
      } : undefined
    };
  }
  
  /**
   * Send a streaming request to Anthropic
   */
  private async *sendAnthropicStreamingRequest(
    model: AIModelConfig,
    prompt: string,
    options: AIModelRequestOptions
  ): AsyncGenerator<AIModelStreamChunk> {
    if (!model.apiKey) {
      throw new Error('Anthropic API key is required');
    }
    
    const temperature = options.temperature ?? model.defaultTemperature;
    const topP = options.topP ?? model.defaultTopP;
    const maxTokens = options.maxTokens ?? model.maxTokens;
    
    const requestBody: any = {
      model: model.modelName,
      messages: [
        { role: 'user', content: prompt }
      ],
      temperature,
      top_p: topP,
      max_tokens: maxTokens,
      stream: true
    };
    
    if (options.stopSequences && options.stopSequences.length > 0) {
      requestBody.stop_sequences = options.stopSequences;
    }
    
    const response = await fetch(model.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': model.apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody),
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Anthropic API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
    }
    
    if (!response.body) {
      throw new Error('Response body is null');
    }
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let done = false;
    
    while (!done) {
      const { value, done: readerDone } = await reader.read();
      done = readerDone;
      
      if (done) {
        yield { content: '', isComplete: true };
        break;
      }
      
      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split('\n').filter(line => line.trim() !== '');
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          
          if (data === '[DONE]') {
            yield { content: '', isComplete: true };
            continue;
          }
          
          try {
            const parsed = JSON.parse(data);
            
            if (parsed.type === 'content_block_delta') {
              const content = parsed.delta?.text || '';
              yield { content, isComplete: false };
            } else if (parsed.type === 'message_stop') {
              yield { content: '', isComplete: true };
            }
          } catch (error) {
            console.error('Error parsing Anthropic stream chunk:', error);
          }
        }
      }
    }
  }
  
  /**
   * Send a request to a custom API
   */
  private async sendCustomRequest(
    model: AIModelConfig,
    prompt: string,
    options: AIModelRequestOptions
  ): Promise<AIModelResponse> {
    // This is a placeholder for custom API integration
    // Implement based on the specific API requirements
    throw new Error('Custom API integration not implemented');
  }
  
  /**
   * Send a streaming request to a custom API
   */
  private async *sendCustomStreamingRequest(
    model: AIModelConfig,
    prompt: string,
    options: AIModelRequestOptions
  ): AsyncGenerator<AIModelStreamChunk> {
    // This is a placeholder for custom API integration
    // Implement based on the specific API requirements
    throw new Error('Custom API streaming integration not implemented');
  }
}

// Export a singleton instance
export const aiModelService = new AIModelService();