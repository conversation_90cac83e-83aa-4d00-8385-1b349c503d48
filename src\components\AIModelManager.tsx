import React, { useState, useEffect } from 'react';
import { AIModelConfig, aiModelService } from '@/services/aiModelService';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Plus, Trash2, Refresh<PERSON><PERSON>, <PERSON>, X, Settings, Zap } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

const AIModelManager: React.FC = () => {
  const { toast } = useToast();
  const [models, setModels] = useState<AIModelConfig[]>([]);
  const [activeModelId, setActiveModelId] = useState<string | null>(null);
  const [isAddingModel, setIsAddingModel] = useState(false);
  const [isEditingModel, setIsEditingModel] = useState(false);
  const [editingModelId, setEditingModelId] = useState<string | null>(null);
  const [newModel, setNewModel] = useState<Partial<AIModelConfig>>({
    name: '',
    provider: 'ollama',
    apiEndpoint: '',
    modelName: '',
    contextLength: 8192,
    supportsStreaming: true,
    supportsVision: false,
    supportsFunctions: false,
    defaultTemperature: 0.7,
    defaultTopP: 0.9,
    defaultTopK: 40,
    maxTokens: 2048,
    isEnabled: true
  });
  const [testPrompt, setTestPrompt] = useState('Hello, can you help me with coding?');
  const [testResponse, setTestResponse] = useState('');
  const [isTesting, setIsTesting] = useState(false);
  
  // Load models on component mount
  useEffect(() => {
    loadModels();
  }, []);
  
  // Load models from the service
  const loadModels = () => {
    const allModels = aiModelService.getModels();
    setModels(allModels);
    
    const activeModel = aiModelService.getActiveModel();
    if (activeModel) {
      setActiveModelId(activeModel.id);
    }
  };
  
  // Set active model
  const handleSetActiveModel = (modelId: string) => {
    const success = aiModelService.setActiveModel(modelId);
    if (success) {
      setActiveModelId(modelId);
      toast({
        title: 'Active Model Changed',
        description: `Active model has been changed successfully.`,
      });
    } else {
      toast({
        title: 'Error',
        description: 'Failed to change active model. Make sure the model is enabled.',
        variant: 'destructive',
      });
    }
  };
  
  // Add new model
  const handleAddModel = () => {
    try {
      if (!newModel.name || !newModel.apiEndpoint || !newModel.modelName) {
        toast({
          title: 'Validation Error',
          description: 'Name, API Endpoint, and Model Name are required.',
          variant: 'destructive',
        });
        return;
      }
      
      const modelId = aiModelService.addModel(newModel as Omit<AIModelConfig, 'id'>);
      loadModels();
      setIsAddingModel(false);
      resetNewModel();
      
      toast({
        title: 'Model Added',
        description: `${newModel.name} has been added successfully.`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to add model: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    }
  };
  
  // Update existing model
  const handleUpdateModel = () => {
    try {
      if (!editingModelId) return;
      
      if (!newModel.name || !newModel.apiEndpoint || !newModel.modelName) {
        toast({
          title: 'Validation Error',
          description: 'Name, API Endpoint, and Model Name are required.',
          variant: 'destructive',
        });
        return;
      }
      
      const success = aiModelService.updateModel(editingModelId, newModel);
      if (success) {
        loadModels();
        setIsEditingModel(false);
        setEditingModelId(null);
        resetNewModel();
        
        toast({
          title: 'Model Updated',
          description: `${newModel.name} has been updated successfully.`,
        });
      } else {
        toast({
          title: 'Error',
          description: 'Failed to update model.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to update model: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    }
  };
  
  // Delete model
  const handleDeleteModel = (modelId: string) => {
    try {
      const success = aiModelService.deleteModel(modelId);
      if (success) {
        loadModels();
        
        toast({
          title: 'Model Deleted',
          description: 'The model has been deleted successfully.',
        });
      } else {
        toast({
          title: 'Error',
          description: 'Failed to delete model.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to delete model: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    }
  };
  
  // Toggle model enabled state
  const handleToggleModelEnabled = (modelId: string, enabled: boolean) => {
    try {
      const success = aiModelService.updateModel(modelId, { isEnabled: enabled });
      if (success) {
        loadModels();
        
        toast({
          title: enabled ? 'Model Enabled' : 'Model Disabled',
          description: `The model has been ${enabled ? 'enabled' : 'disabled'} successfully.`,
        });
      } else {
        toast({
          title: 'Error',
          description: `Failed to ${enabled ? 'enable' : 'disable'} model.`,
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to update model: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    }
  };
  
  // Edit model
  const handleEditModel = (modelId: string) => {
    const model = models.find(m => m.id === modelId);
    if (model) {
      setNewModel({ ...model });
      setEditingModelId(modelId);
      setIsEditingModel(true);
    }
  };
  
  // Reset new model form
  const resetNewModel = () => {
    setNewModel({
      name: '',
      provider: 'ollama',
      apiEndpoint: '',
      modelName: '',
      contextLength: 8192,
      supportsStreaming: true,
      supportsVision: false,
      supportsFunctions: false,
      defaultTemperature: 0.7,
      defaultTopP: 0.9,
      defaultTopK: 40,
      maxTokens: 2048,
      isEnabled: true
    });
  };
  
  // Test model
  const handleTestModel = async (modelId: string) => {
    try {
      setIsTesting(true);
      setTestResponse('');
      
      // Set the model as active temporarily
      const previousActiveModelId = activeModelId;
      aiModelService.setActiveModel(modelId);
      
      // Send a test request
      const response = await aiModelService.sendRequest(testPrompt);
      
      // Restore the previous active model
      if (previousActiveModelId) {
        aiModelService.setActiveModel(previousActiveModelId);
      }
      
      setTestResponse(response.content);
      
      toast({
        title: 'Test Completed',
        description: 'The model test has been completed successfully.',
      });
    } catch (error) {
      setTestResponse(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      toast({
        title: 'Test Failed',
        description: `Failed to test model: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    } finally {
      setIsTesting(false);
    }
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>AI Model Manager</span>
          <Button variant="outline" size="sm" onClick={() => setIsAddingModel(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Model
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="models">
          <TabsList className="mb-4">
            <TabsTrigger value="models">Models</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
            <TabsTrigger value="test">Test</TabsTrigger>
          </TabsList>
          
          <TabsContent value="models" className="space-y-4">
            {models.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <p>No AI models configured</p>
                <p className="text-sm">Click "Add Model" to add your first AI model</p>
              </div>
            ) : (
              models.map(model => (
                <div 
                  key={model.id}
                  className={`p-4 rounded-lg border ${model.id === activeModelId ? 'border-primary bg-primary/5' : 'border-border'}`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex flex-col">
                      <div className="flex items-center">
                        <span className="font-medium text-lg">{model.name}</span>
                        {model.id === activeModelId && (
                          <Badge variant="default" className="ml-2">Active</Badge>
                        )}
                        {!model.isEnabled && (
                          <Badge variant="outline" className="ml-2">Disabled</Badge>
                        )}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {model.provider.charAt(0).toUpperCase() + model.provider.slice(1)} • {model.modelName}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleTestModel(model.id)}
                      >
                        <Zap className="h-4 w-4 mr-1" />
                        Test
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleEditModel(model.id)}
                      >
                        <Settings className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                      <Button 
                        variant={model.id === activeModelId ? 'default' : 'outline'} 
                        size="sm"
                        onClick={() => handleSetActiveModel(model.id)}
                        disabled={!model.isEnabled || model.id === activeModelId}
                      >
                        {model.id === activeModelId ? (
                          <Check className="h-4 w-4 mr-1" />
                        ) : null}
                        {model.id === activeModelId ? 'Active' : 'Set Active'}
                      </Button>
                    </div>
                  </div>
                  
                  <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">API Endpoint:</span> {model.apiEndpoint}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Context Length:</span> {model.contextLength.toLocaleString()} tokens
                    </div>
                    <div>
                      <span className="text-muted-foreground">Temperature:</span> {model.defaultTemperature}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Max Tokens:</span> {model.maxTokens.toLocaleString()}
                    </div>
                  </div>
                  
                  <div className="mt-4 flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <Switch 
                        id={`model-enabled-${model.id}`}
                        checked={model.isEnabled}
                        onCheckedChange={(checked) => handleToggleModelEnabled(model.id, checked)}
                      />
                      <Label htmlFor={`model-enabled-${model.id}`}>Enabled</Label>
                    </div>
                    
                    <div className="flex-1"></div>
                    
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleDeleteModel(model.id)}
                      className="text-destructive hover:text-destructive hover:bg-destructive/10"
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                </div>
              ))
            )}
          </TabsContent>
          
          <TabsContent value="settings" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Global Settings</h3>
                
                <div className="space-y-2">
                  <Label htmlFor="default-temperature">Default Temperature</Label>
                  <div className="flex items-center space-x-4">
                    <Slider 
                      id="default-temperature"
                      min={0} 
                      max={1} 
                      step={0.1} 
                      value={[0.7]} 
                      className="flex-1"
                    />
                    <span className="w-12 text-center">0.7</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="default-max-tokens">Default Max Tokens</Label>
                  <div className="flex items-center space-x-4">
                    <Slider 
                      id="default-max-tokens"
                      min={256} 
                      max={4096} 
                      step={256} 
                      value={[2048]} 
                      className="flex-1"
                    />
                    <span className="w-12 text-center">2048</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="default-model">Default Model</Label>
                  <Select defaultValue={activeModelId || undefined}>
                    <SelectTrigger id="default-model">
                      <SelectValue placeholder="Select a model" />
                    </SelectTrigger>
                    <SelectContent>
                      {models.filter(m => m.isEnabled).map(model => (
                        <SelectItem key={model.id} value={model.id}>{model.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">API Keys</h3>
                
                <div className="space-y-2">
                  <Label htmlFor="openai-api-key">OpenAI API Key</Label>
                  <Input 
                    id="openai-api-key"
                    type="password"
                    placeholder="sk-..."
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="anthropic-api-key">Anthropic API Key</Label>
                  <Input 
                    id="anthropic-api-key"
                    type="password"
                    placeholder="sk-ant-..."
                  />
                </div>
                
                <Button className="mt-4">
                  Save API Keys
                </Button>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="test" className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="test-model">Select Model to Test</Label>
                <Select defaultValue={activeModelId || undefined}>
                  <SelectTrigger id="test-model">
                    <SelectValue placeholder="Select a model" />
                  </SelectTrigger>
                  <SelectContent>
                    {models.filter(m => m.isEnabled).map(model => (
                      <SelectItem key={model.id} value={model.id}>{model.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="test-prompt">Test Prompt</Label>
                <Input 
                  id="test-prompt"
                  value={testPrompt}
                  onChange={(e) => setTestPrompt(e.target.value)}
                  placeholder="Enter a test prompt..."
                />
              </div>
              
              <Button 
                onClick={() => activeModelId && handleTestModel(activeModelId)}
                disabled={isTesting || !activeModelId}
              >
                {isTesting ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Testing...
                  </>
                ) : (
                  <>
                    <Zap className="h-4 w-4 mr-2" />
                    Run Test
                  </>
                )}
              </Button>
              
              {testResponse && (
                <div className="mt-4">
                  <Label>Response</Label>
                  <div className="p-4 bg-muted rounded-md mt-2 whitespace-pre-wrap">
                    {testResponse}
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      
      {/* Add Model Dialog */}
      <Dialog open={isAddingModel} onOpenChange={setIsAddingModel}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New AI Model</DialogTitle>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="model-name">Model Name</Label>
                <Input 
                  id="model-name"
                  value={newModel.name}
                  onChange={(e) => setNewModel({...newModel, name: e.target.value})}
                  placeholder="e.g., GPT-4 Turbo"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="model-provider">Provider</Label>
                <Select 
                  value={newModel.provider}
                  onValueChange={(value) => setNewModel({...newModel, provider: value as any})}
                >
                  <SelectTrigger id="model-provider">
                    <SelectValue placeholder="Select provider" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ollama">Ollama</SelectItem>
                    <SelectItem value="openai">OpenAI</SelectItem>
                    <SelectItem value="anthropic">Anthropic</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="model-api-endpoint">API Endpoint</Label>
              <Input 
                id="model-api-endpoint"
                value={newModel.apiEndpoint}
                onChange={(e) => setNewModel({...newModel, apiEndpoint: e.target.value})}
                placeholder="e.g., https://api.openai.com/v1/chat/completions"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="model-name-id">Model Name/ID</Label>
                <Input 
                  id="model-name-id"
                  value={newModel.modelName}
                  onChange={(e) => setNewModel({...newModel, modelName: e.target.value})}
                  placeholder="e.g., gpt-4-turbo"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="model-api-key">API Key (if required)</Label>
                <Input 
                  id="model-api-key"
                  type="password"
                  value={newModel.apiKey || ''}
                  onChange={(e) => setNewModel({...newModel, apiKey: e.target.value})}
                  placeholder="API Key"
                />
              </div>
            </div>
            
            <Separator />
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="model-context-length">Context Length (tokens)</Label>
                <Input 
                  id="model-context-length"
                  type="number"
                  value={newModel.contextLength}
                  onChange={(e) => setNewModel({...newModel, contextLength: parseInt(e.target.value) || 0})}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="model-max-tokens">Max Output Tokens</Label>
                <Input 
                  id="model-max-tokens"
                  type="number"
                  value={newModel.maxTokens}
                  onChange={(e) => setNewModel({...newModel, maxTokens: parseInt(e.target.value) || 0})}
                />
              </div>
            </div>
            
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="model-temperature">Temperature</Label>
                <Input 
                  id="model-temperature"
                  type="number"
                  step="0.1"
                  min="0"
                  max="2"
                  value={newModel.defaultTemperature}
                  onChange={(e) => setNewModel({...newModel, defaultTemperature: parseFloat(e.target.value) || 0})}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="model-top-p">Top P</Label>
                <Input 
                  id="model-top-p"
                  type="number"
                  step="0.1"
                  min="0"
                  max="1"
                  value={newModel.defaultTopP}
                  onChange={(e) => setNewModel({...newModel, defaultTopP: parseFloat(e.target.value) || 0})}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="model-top-k">Top K</Label>
                <Input 
                  id="model-top-k"
                  type="number"
                  step="1"
                  min="0"
                  value={newModel.defaultTopK}
                  onChange={(e) => setNewModel({...newModel, defaultTopK: parseInt(e.target.value) || 0})}
                />
              </div>
            </div>
            
            <Separator />
            
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Switch 
                  id="model-streaming"
                  checked={newModel.supportsStreaming}
                  onCheckedChange={(checked) => setNewModel({...newModel, supportsStreaming: checked})}
                />
                <Label htmlFor="model-streaming">Supports Streaming</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch 
                  id="model-vision"
                  checked={newModel.supportsVision}
                  onCheckedChange={(checked) => setNewModel({...newModel, supportsVision: checked})}
                />
                <Label htmlFor="model-vision">Supports Vision</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch 
                  id="model-functions"
                  checked={newModel.supportsFunctions}
                  onCheckedChange={(checked) => setNewModel({...newModel, supportsFunctions: checked})}
                />
                <Label htmlFor="model-functions">Supports Functions</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch 
                  id="model-enabled"
                  checked={newModel.isEnabled}
                  onCheckedChange={(checked) => setNewModel({...newModel, isEnabled: checked})}
                />
                <Label htmlFor="model-enabled">Enabled</Label>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsAddingModel(false);
              resetNewModel();
            }}>
              Cancel
            </Button>
            <Button onClick={handleAddModel}>Add Model</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Edit Model Dialog */}
      <Dialog open={isEditingModel} onOpenChange={setIsEditingModel}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit AI Model</DialogTitle>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-model-name">Model Name</Label>
                <Input 
                  id="edit-model-name"
                  value={newModel.name}
                  onChange={(e) => setNewModel({...newModel, name: e.target.value})}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="edit-model-provider">Provider</Label>
                <Select 
                  value={newModel.provider}
                  onValueChange={(value) => setNewModel({...newModel, provider: value as any})}
                >
                  <SelectTrigger id="edit-model-provider">
                    <SelectValue placeholder="Select provider" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ollama">Ollama</SelectItem>
                    <SelectItem value="openai">OpenAI</SelectItem>
                    <SelectItem value="anthropic">Anthropic</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-model-api-endpoint">API Endpoint</Label>
              <Input 
                id="edit-model-api-endpoint"
                value={newModel.apiEndpoint}
                onChange={(e) => setNewModel({...newModel, apiEndpoint: e.target.value})}
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-model-name-id">Model Name/ID</Label>
                <Input 
                  id="edit-model-name-id"
                  value={newModel.modelName}
                  onChange={(e) => setNewModel({...newModel, modelName: e.target.value})}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="edit-model-api-key">API Key (if required)</Label>
                <Input 
                  id="edit-model-api-key"
                  type="password"
                  value={newModel.apiKey || ''}
                  onChange={(e) => setNewModel({...newModel, apiKey: e.target.value})}
                  placeholder={newModel.apiKey ? '••••••••' : 'No API Key set'}
                />
              </div>
            </div>
            
            <Separator />
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-model-context-length">Context Length (tokens)</Label>
                <Input 
                  id="edit-model-context-length"
                  type="number"
                  value={newModel.contextLength}
                  onChange={(e) => setNewModel({...newModel, contextLength: parseInt(e.target.value) || 0})}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="edit-model-max-tokens">Max Output Tokens</Label>
                <Input 
                  id="edit-model-max-tokens"
                  type="number"
                  value={newModel.maxTokens}
                  onChange={(e) => setNewModel({...newModel, maxTokens: parseInt(e.target.value) || 0})}
                />
              </div>
            </div>
            
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-model-temperature">Temperature</Label>
                <Input 
                  id="edit-model-temperature"
                  type="number"
                  step="0.1"
                  min="0"
                  max="2"
                  value={newModel.defaultTemperature}
                  onChange={(e) => setNewModel({...newModel, defaultTemperature: parseFloat(e.target.value) || 0})}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="edit-model-top-p">Top P</Label>
                <Input 
                  id="edit-model-top-p"
                  type="number"
                  step="0.1"
                  min="0"
                  max="1"
                  value={newModel.defaultTopP}
                  onChange={(e) => setNewModel({...newModel, defaultTopP: parseFloat(e.target.value) || 0})}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="edit-model-top-k">Top K</Label>
                <Input 
                  id="edit-model-top-k"
                  type="number"
                  step="1"
                  min="0"
                  value={newModel.defaultTopK}
                  onChange={(e) => setNewModel({...newModel, defaultTopK: parseInt(e.target.value) || 0})}
                />
              </div>
            </div>
            
            <Separator />
            
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Switch 
                  id="edit-model-streaming"
                  checked={newModel.supportsStreaming}
                  onCheckedChange={(checked) => setNewModel({...newModel, supportsStreaming: checked})}
                />
                <Label htmlFor="edit-model-streaming">Supports Streaming</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch 
                  id="edit-model-vision"
                  checked={newModel.supportsVision}
                  onCheckedChange={(checked) => setNewModel({...newModel, supportsVision: checked})}
                />
                <Label htmlFor="edit-model-vision">Supports Vision</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch 
                  id="edit-model-functions"
                  checked={newModel.supportsFunctions}
                  onCheckedChange={(checked) => setNewModel({...newModel, supportsFunctions: checked})}
                />
                <Label htmlFor="edit-model-functions">Supports Functions</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch 
                  id="edit-model-enabled"
                  checked={newModel.isEnabled}
                  onCheckedChange={(checked) => setNewModel({...newModel, isEnabled: checked})}
                />
                <Label htmlFor="edit-model-enabled">Enabled</Label>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsEditingModel(false);
              setEditingModelId(null);
              resetNewModel();
            }}>
              Cancel
            </Button>
            <Button onClick={handleUpdateModel}>Update Model</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default AIModelManager;