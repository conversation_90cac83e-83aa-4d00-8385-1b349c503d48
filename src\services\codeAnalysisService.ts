import { FileNode } from "@/components/FileExplorer";

interface CodeAnalysisResult {
  dependencies: Record<string, string[]>;
  imports: Record<string, string[]>;
  exports: Record<string, string[]>;
  functions: Record<string, {
    name: string;
    params: string[];
    returnType?: string;
    location: { file: string; line: number };
    complexity: number;
    description?: string;
  }[]>;
  classes: Record<string, {
    name: string;
    methods: string[];
    properties: string[];
    location: { file: string; line: number };
    description?: string;
  }[]>;
  projectStructure: {
    rootDirectories: string[];
    fileCount: number;
    directoryCount: number;
    fileTypes: Record<string, number>;
  };
}

/**
 * Analyzes the entire codebase to extract meaningful information
 * about code structure, dependencies, and relationships
 */
export async function analyzeCodebase(files: FileNode[]): Promise<CodeAnalysisResult> {
  // Initialize the result structure
  const result: CodeAnalysisResult = {
    dependencies: {},
    imports: {},
    exports: {},
    functions: {},
    classes: {},
    projectStructure: {
      rootDirectories: [],
      fileCount: 0,
      directoryCount: 0,
      fileTypes: {}
    }
  };
  
  // Extract root directories
  result.projectStructure.rootDirectories = files
    .filter(node => node.type === 'folder')
    .map(node => node.name);
  
  // Analyze each file
  const analyzeNode = (node: FileNode, path: string = '') => {
    const currentPath = path ? `${path}/${node.name}` : node.name;
    
    if (node.type === 'folder' && node.children) {
      result.projectStructure.directoryCount++;
      node.children.forEach(child => analyzeNode(child, currentPath));
    } else if (node.type === 'file') {
      result.projectStructure.fileCount++;
      
      // Count file types
      const extension = node.name.split('.').pop() || 'unknown';
      result.projectStructure.fileTypes[extension] = (result.projectStructure.fileTypes[extension] || 0) + 1;
      
      // Analyze file content based on file type
      if (node.content) {
        switch (extension) {
          case 'js':
          case 'jsx':
          case 'ts':
          case 'tsx':
            analyzeJavaScriptFile(node, currentPath, result);
            break;
          case 'py':
            analyzePythonFile(node, currentPath, result);
            break;
          case 'java':
            analyzeJavaFile(node, currentPath, result);
            break;
          case 'json':
            analyzeJsonFile(node, currentPath, result);
            break;
          // Add more file types as needed
        }
      }
    }
  };
  
  // Start analysis from root nodes
  files.forEach(node => analyzeNode(node));
  
  return result;
}

/**
 * Analyzes JavaScript/TypeScript files to extract imports, exports, functions, and classes
 */
function analyzeJavaScriptFile(file: FileNode, path: string, result: CodeAnalysisResult) {
  if (!file.content) return;
  
  const content = file.content;
  const lines = content.split('\n');
  
  // Extract imports
  const importRegex = /import\s+(?:{([^}]+)}|\*\s+as\s+([a-zA-Z0-9_$]+)|([a-zA-Z0-9_$]+))\s+from\s+['"]([^'"]+)['"]/g;
  let match;
  const imports: string[] = [];
  
  while ((match = importRegex.exec(content)) !== null) {
    const namedImports = match[1];
    const namespaceImport = match[2];
    const defaultImport = match[3];
    const source = match[4];
    
    if (source) {
      imports.push(source);
      
      // Add to dependencies if it's a package (doesn't start with . or /)
      if (!source.startsWith('.') && !source.startsWith('/') && !source.startsWith('@/')) {
        if (!result.dependencies[path]) {
          result.dependencies[path] = [];
        }
        if (!result.dependencies[path].includes(source)) {
          result.dependencies[path].push(source);
        }
      }
    }
  }
  
  if (imports.length > 0) {
    result.imports[path] = imports;
  }
  
  // Extract exports
  const exportRegex = /export\s+(?:default\s+)?(?:function|class|const|let|var)\s+([a-zA-Z0-9_$]+)/g;
  const exports: string[] = [];
  
  while ((match = exportRegex.exec(content)) !== null) {
    const exportName = match[1];
    if (exportName) {
      exports.push(exportName);
    }
  }
  
  if (exports.length > 0) {
    result.exports[path] = exports;
  }
  
  // Extract functions
  const functionRegex = /(?:function|const|let|var)\s+([a-zA-Z0-9_$]+)\s*=?\s*(?:function)?\s*\(([^)]*)\)/g;
  const functions: {
    name: string;
    params: string[];
    location: { file: string; line: number };
    complexity: number;
  }[] = [];
  
  while ((match = functionRegex.exec(content)) !== null) {
    const functionName = match[1];
    const params = match[2].split(',').map(param => param.trim()).filter(Boolean);
    
    // Find line number
    const lineIndex = content.substring(0, match.index).split('\n').length;
    
    // Simple complexity calculation (number of if/else/for/while statements)
    const functionBody = extractFunctionBody(content, match.index);
    const complexity = calculateComplexity(functionBody);
    
    functions.push({
      name: functionName,
      params,
      location: { file: path, line: lineIndex },
      complexity
    });
  }
  
  if (functions.length > 0) {
    result.functions[path] = functions;
  }
  
  // Extract classes
  const classRegex = /class\s+([a-zA-Z0-9_$]+)(?:\s+extends\s+([a-zA-Z0-9_$]+))?\s*{/g;
  const classes: {
    name: string;
    methods: string[];
    properties: string[];
    location: { file: string; line: number };
  }[] = [];
  
  while ((match = classRegex.exec(content)) !== null) {
    const className = match[1];
    
    // Find line number
    const lineIndex = content.substring(0, match.index).split('\n').length;
    
    // Extract class body
    const classBody = extractClassBody(content, match.index);
    
    // Extract methods
    const methodRegex = /(?:async\s+)?([a-zA-Z0-9_$]+)\s*\([^)]*\)\s*{/g;
    const methods: string[] = [];
    let methodMatch;
    
    while ((methodMatch = methodRegex.exec(classBody)) !== null) {
      methods.push(methodMatch[1]);
    }
    
    // Extract properties
    const propertyRegex = /(?:this\.)?([a-zA-Z0-9_$]+)\s*=\s*|([a-zA-Z0-9_$]+)\s*=/g;
    const properties: string[] = [];
    let propertyMatch;
    
    while ((propertyMatch = propertyRegex.exec(classBody)) !== null) {
      const propertyName = propertyMatch[1] || propertyMatch[2];
      if (propertyName && !methods.includes(propertyName)) {
        properties.push(propertyName);
      }
    }
    
    classes.push({
      name: className,
      methods,
      properties,
      location: { file: path, line: lineIndex }
    });
  }
  
  if (classes.length > 0) {
    result.classes[path] = classes;
  }
}

/**
 * Analyzes Python files to extract imports, functions, and classes
 */
function analyzePythonFile(file: FileNode, path: string, result: CodeAnalysisResult) {
  if (!file.content) return;
  
  const content = file.content;
  const lines = content.split('\n');
  
  // Extract imports
  const importRegex = /(?:import\s+([a-zA-Z0-9_$.]+)|from\s+([a-zA-Z0-9_$.]+)\s+import\s+(?:[a-zA-Z0-9_$, ]+|\*))/g;
  let match;
  const imports: string[] = [];
  
  while ((match = importRegex.exec(content)) !== null) {
    const importName = match[1] || match[2];
    if (importName) {
      imports.push(importName);
      
      // Add to dependencies if it's a package (doesn't contain a dot)
      const basePackage = importName.split('.')[0];
      if (!basePackage.startsWith('.')) {
        if (!result.dependencies[path]) {
          result.dependencies[path] = [];
        }
        if (!result.dependencies[path].includes(basePackage)) {
          result.dependencies[path].push(basePackage);
        }
      }
    }
  }
  
  if (imports.length > 0) {
    result.imports[path] = imports;
  }
  
  // Extract functions
  const functionRegex = /def\s+([a-zA-Z0-9_$]+)\s*\(([^)]*)\)/g;
  const functions: {
    name: string;
    params: string[];
    location: { file: string; line: number };
    complexity: number;
  }[] = [];
  
  while ((match = functionRegex.exec(content)) !== null) {
    const functionName = match[1];
    const params = match[2].split(',').map(param => param.trim()).filter(Boolean);
    
    // Find line number
    const lineIndex = content.substring(0, match.index).split('\n').length;
    
    // Simple complexity calculation
    const functionBody = extractPythonFunctionBody(content, match.index);
    const complexity = calculateComplexity(functionBody);
    
    functions.push({
      name: functionName,
      params,
      location: { file: path, line: lineIndex },
      complexity
    });
  }
  
  if (functions.length > 0) {
    result.functions[path] = functions;
  }
  
  // Extract classes
  const classRegex = /class\s+([a-zA-Z0-9_$]+)(?:\s*\(([^)]*)\))?\s*:/g;
  const classes: {
    name: string;
    methods: string[];
    properties: string[];
    location: { file: string; line: number };
  }[] = [];
  
  while ((match = classRegex.exec(content)) !== null) {
    const className = match[1];
    
    // Find line number
    const lineIndex = content.substring(0, match.index).split('\n').length;
    
    // Extract class body
    const classBody = extractPythonClassBody(content, match.index);
    
    // Extract methods
    const methodRegex = /def\s+([a-zA-Z0-9_$]+)\s*\([^)]*\)/g;
    const methods: string[] = [];
    let methodMatch;
    
    while ((methodMatch = methodRegex.exec(classBody)) !== null) {
      methods.push(methodMatch[1]);
    }
    
    // Extract properties (simple approach)
    const propertyRegex = /self\.([a-zA-Z0-9_$]+)\s*=/g;
    const properties: string[] = [];
    let propertyMatch;
    
    while ((propertyMatch = propertyRegex.exec(classBody)) !== null) {
      properties.push(propertyMatch[1]);
    }
    
    classes.push({
      name: className,
      methods,
      properties,
      location: { file: path, line: lineIndex }
    });
  }
  
  if (classes.length > 0) {
    result.classes[path] = classes;
  }
}

/**
 * Analyzes Java files to extract imports, classes, and methods
 */
function analyzeJavaFile(file: FileNode, path: string, result: CodeAnalysisResult) {
  if (!file.content) return;
  
  const content = file.content;
  
  // Extract imports
  const importRegex = /import\s+([a-zA-Z0-9_.]+(?:\.[*])?);/g;
  let match;
  const imports: string[] = [];
  
  while ((match = importRegex.exec(content)) !== null) {
    const importName = match[1];
    if (importName) {
      imports.push(importName);
      
      // Add to dependencies
      const basePackage = importName.split('.')[0];
      if (!result.dependencies[path]) {
        result.dependencies[path] = [];
      }
      if (!result.dependencies[path].includes(basePackage)) {
        result.dependencies[path].push(basePackage);
      }
    }
  }
  
  if (imports.length > 0) {
    result.imports[path] = imports;
  }
  
  // Extract classes and methods (simplified)
  const classRegex = /(?:public|private|protected)?\s+(?:abstract|final)?\s+class\s+([a-zA-Z0-9_$]+)(?:\s+extends\s+([a-zA-Z0-9_$]+))?(?:\s+implements\s+([a-zA-Z0-9_$,\s]+))?\s*{/g;
  
  while ((match = classRegex.exec(content)) !== null) {
    const className = match[1];
    const lineIndex = content.substring(0, match.index).split('\n').length;
    
    // Extract class body
    const classBody = extractJavaClassBody(content, match.index);
    
    // Extract methods
    const methodRegex = /(?:public|private|protected)?\s+(?:static|final|abstract)?\s+(?:[a-zA-Z0-9_$<>[\],\s]+)\s+([a-zA-Z0-9_$]+)\s*\([^)]*\)\s*(?:throws\s+[a-zA-Z0-9_$,\s]+)?\s*{/g;
    const methods: string[] = [];
    let methodMatch;
    
    while ((methodMatch = methodRegex.exec(classBody)) !== null) {
      methods.push(methodMatch[1]);
    }
    
    // Extract properties
    const propertyRegex = /(?:private|protected|public)\s+(?:static|final)?\s+(?:[a-zA-Z0-9_$<>[\],\s]+)\s+([a-zA-Z0-9_$]+)\s*(?:=|;)/g;
    const properties: string[] = [];
    let propertyMatch;
    
    while ((propertyMatch = propertyRegex.exec(classBody)) !== null) {
      properties.push(propertyMatch[1]);
    }
    
    if (!result.classes[path]) {
      result.classes[path] = [];
    }
    
    result.classes[path].push({
      name: className,
      methods,
      properties,
      location: { file: path, line: lineIndex }
    });
  }
}

/**
 * Analyzes JSON files to extract structure and dependencies
 */
function analyzeJsonFile(file: FileNode, path: string, result: CodeAnalysisResult) {
  if (!file.content) return;
  
  try {
    const json = JSON.parse(file.content);
    
    // Check if it's a package.json file
    if (file.name === 'package.json') {
      // Extract dependencies
      const dependencies = {
        ...(json.dependencies || {}),
        ...(json.devDependencies || {})
      };
      
      if (Object.keys(dependencies).length > 0) {
        result.dependencies[path] = Object.keys(dependencies);
      }
    }
  } catch (error) {
    console.error(`Error parsing JSON file ${path}:`, error);
  }
}

// Helper functions

function extractFunctionBody(content: string, startIndex: number): string {
  let openBraces = 0;
  let startBraceIndex = content.indexOf('{', startIndex);
  
  if (startBraceIndex === -1) return '';
  
  let endBraceIndex = startBraceIndex;
  
  for (let i = startBraceIndex; i < content.length; i++) {
    if (content[i] === '{') {
      openBraces++;
    } else if (content[i] === '}') {
      openBraces--;
      if (openBraces === 0) {
        endBraceIndex = i;
        break;
      }
    }
  }
  
  return content.substring(startBraceIndex, endBraceIndex + 1);
}

function extractClassBody(content: string, startIndex: number): string {
  let openBraces = 0;
  let startBraceIndex = content.indexOf('{', startIndex);
  
  if (startBraceIndex === -1) return '';
  
  let endBraceIndex = startBraceIndex;
  
  for (let i = startBraceIndex; i < content.length; i++) {
    if (content[i] === '{') {
      openBraces++;
    } else if (content[i] === '}') {
      openBraces--;
      if (openBraces === 0) {
        endBraceIndex = i;
        break;
      }
    }
  }
  
  return content.substring(startBraceIndex, endBraceIndex + 1);
}

function extractPythonFunctionBody(content: string, startIndex: number): string {
  const lines = content.split('\n');
  const startLine = content.substring(0, startIndex).split('\n').length - 1;
  
  let currentLine = startLine + 1;
  const indentation = lines[currentLine].match(/^\s*/)?.[0].length || 0;
  
  let functionBody = '';
  
  while (currentLine < lines.length) {
    const line = lines[currentLine];
    const currentIndentation = line.match(/^\s*/)?.[0].length || 0;
    
    if (line.trim() === '' || currentIndentation > indentation) {
      functionBody += line + '\n';
      currentLine++;
    } else {
      break;
    }
  }
  
  return functionBody;
}

function extractPythonClassBody(content: string, startIndex: number): string {
  const lines = content.split('\n');
  const startLine = content.substring(0, startIndex).split('\n').length - 1;
  
  let currentLine = startLine + 1;
  const indentation = lines[currentLine].match(/^\s*/)?.[0].length || 0;
  
  let classBody = '';
  
  while (currentLine < lines.length) {
    const line = lines[currentLine];
    const currentIndentation = line.match(/^\s*/)?.[0].length || 0;
    
    if (line.trim() === '' || currentIndentation > indentation) {
      classBody += line + '\n';
      currentLine++;
    } else {
      break;
    }
  }
  
  return classBody;
}

function extractJavaClassBody(content: string, startIndex: number): string {
  let openBraces = 0;
  let startBraceIndex = content.indexOf('{', startIndex);
  
  if (startBraceIndex === -1) return '';
  
  let endBraceIndex = startBraceIndex;
  
  for (let i = startBraceIndex; i < content.length; i++) {
    if (content[i] === '{') {
      openBraces++;
    } else if (content[i] === '}') {
      openBraces--;
      if (openBraces === 0) {
        endBraceIndex = i;
        break;
      }
    }
  }
  
  return content.substring(startBraceIndex, endBraceIndex + 1);
}

function calculateComplexity(code: string): number {
  // Simple cyclomatic complexity calculation
  // Count control flow statements
  const controlFlowRegex = /\b(if|else|for|while|switch|case|catch|return|break|continue)\b/g;
  const matches = code.match(controlFlowRegex) || [];
  
  // Base complexity is 1
  return 1 + matches.length;
}

/**
 * Generates a dependency graph for visualization
 */
export function generateDependencyGraph(analysisResult: CodeAnalysisResult) {
  const nodes: { id: string; label: string; type: string }[] = [];
  const edges: { from: string; to: string; label?: string }[] = [];
  
  // Add files as nodes
  Object.keys(analysisResult.imports).forEach(file => {
    nodes.push({
      id: file,
      label: file.split('/').pop() || file,
      type: 'file'
    });
  });
  
  // Add dependencies as nodes
  const allDependencies = new Set<string>();
  Object.values(analysisResult.dependencies).forEach(deps => {
    deps.forEach(dep => allDependencies.add(dep));
  });
  
  allDependencies.forEach(dep => {
    nodes.push({
      id: `dep_${dep}`,
      label: dep,
      type: 'dependency'
    });
  });
  
  // Add edges for imports
  Object.entries(analysisResult.imports).forEach(([file, imports]) => {
    imports.forEach(importPath => {
      // Check if it's a dependency or a local file
      if (!importPath.startsWith('.') && !importPath.startsWith('/')) {
        // It's a dependency
        edges.push({
          from: file,
          to: `dep_${importPath.split('/')[0]}`,
          label: 'imports'
        });
      } else {
        // It's a local file - resolve the path
        const resolvedPath = resolveImportPath(file, importPath);
        if (resolvedPath && Object.keys(analysisResult.imports).includes(resolvedPath)) {
          edges.push({
            from: file,
            to: resolvedPath,
            label: 'imports'
          });
        }
      }
    });
  });
  
  return { nodes, edges };
}

/**
 * Resolves a relative import path to an absolute path
 */
function resolveImportPath(currentFile: string, importPath: string): string | null {
  if (!importPath.startsWith('.')) return null;
  
  const parts = currentFile.split('/');
  parts.pop(); // Remove the filename
  
  const importParts = importPath.split('/');
  
  for (const part of importParts) {
    if (part === '.') {
      // Current directory, do nothing
    } else if (part === '..') {
      // Parent directory
      parts.pop();
    } else {
      parts.push(part);
    }
  }
  
  // Add .js extension if not present
  if (!parts[parts.length - 1].includes('.')) {
    parts[parts.length - 1] += '.js';
  }
  
  return parts.join('/');
}

/**
 * Identifies potential code issues and suggests improvements
 */
export function identifyCodeIssues(files: FileNode[]): {
  file: string;
  line: number;
  severity: 'error' | 'warning' | 'info';
  message: string;
  suggestion?: string;
}[] {
  const issues: {
    file: string;
    line: number;
    severity: 'error' | 'warning' | 'info';
    message: string;
    suggestion?: string;
  }[] = [];
  
  // Analyze each file
  const analyzeNode = (node: FileNode, path: string = '') => {
    const currentPath = path ? `${path}/${node.name}` : node.name;
    
    if (node.type === 'folder' && node.children) {
      node.children.forEach(child => analyzeNode(child, currentPath));
    } else if (node.type === 'file' && node.content) {
      const extension = node.name.split('.').pop() || '';
      
      // Check for common issues based on file type
      switch (extension) {
        case 'js':
        case 'jsx':
        case 'ts':
        case 'tsx':
          identifyJavaScriptIssues(node.content, currentPath, issues);
          break;
        case 'py':
          identifyPythonIssues(node.content, currentPath, issues);
          break;
        case 'css':
          identifyCSSIssues(node.content, currentPath, issues);
          break;
        // Add more file types as needed
      }
    }
  };
  
  // Start analysis from root nodes
  files.forEach(node => analyzeNode(node));
  
  return issues;
}

/**
 * Identifies common issues in JavaScript/TypeScript files
 */
function identifyJavaScriptIssues(content: string, filePath: string, issues: any[]) {
  const lines = content.split('\n');
  
  // Check for console.log statements
  const consoleRegex = /console\.(log|warn|error|info|debug)/g;
  let match;
  
  while ((match = consoleRegex.exec(content)) !== null) {
    const lineNumber = content.substring(0, match.index).split('\n').length;
    
    issues.push({
      file: filePath,
      line: lineNumber,
      severity: 'warning',
      message: `Console statement (${match[1]}) found in production code`,
      suggestion: 'Remove console statements from production code or use a logger'
    });
  }
  
  // Check for TODO comments
  const todoRegex = /\/\/\s*TODO/g;
  
  while ((match = todoRegex.exec(content)) !== null) {
    const lineNumber = content.substring(0, match.index).split('\n').length;
    
    issues.push({
      file: filePath,
      line: lineNumber,
      severity: 'info',
      message: 'TODO comment found',
      suggestion: 'Consider addressing the TODO item'
    });
  }
  
  // Check for long lines
  lines.forEach((line, index) => {
    if (line.length > 100) {
      issues.push({
        file: filePath,
        line: index + 1,
        severity: 'info',
        message: 'Line exceeds 100 characters',
        suggestion: 'Consider breaking this line into multiple lines for better readability'
      });
    }
  });
  
  // Check for empty catch blocks
  const emptyCatchRegex = /catch\s*\([^)]*\)\s*{\s*}/g;
  
  while ((match = emptyCatchRegex.exec(content)) !== null) {
    const lineNumber = content.substring(0, match.index).split('\n').length;
    
    issues.push({
      file: filePath,
      line: lineNumber,
      severity: 'warning',
      message: 'Empty catch block',
      suggestion: 'Add error handling or logging to the catch block'
    });
  }
  
  // Check for potential memory leaks in React components
  if (filePath.includes('component') || content.includes('React.Component') || content.includes('useState')) {
    // Check for event listeners without cleanup
    const addEventListenerRegex = /addEventListener\(/g;
    const removeEventListenerRegex = /removeEventListener\(/g;
    
    const addListenerCount = (content.match(addEventListenerRegex) || []).length;
    const removeListenerCount = (content.match(removeEventListenerRegex) || []).length;
    
    if (addListenerCount > removeListenerCount) {
      issues.push({
        file: filePath,
        line: 1,
        severity: 'warning',
        message: 'Potential memory leak: more event listeners added than removed',
        suggestion: 'Ensure all event listeners are removed in useEffect cleanup or componentWillUnmount'
      });
    }
  }
}

/**
 * Identifies common issues in Python files
 */
function identifyPythonIssues(content: string, filePath: string, issues: any[]) {
  const lines = content.split('\n');
  
  // Check for print statements
  const printRegex = /print\s*\(/g;
  let match;
  
  while ((match = printRegex.exec(content)) !== null) {
    const lineNumber = content.substring(0, match.index).split('\n').length;
    
    issues.push({
      file: filePath,
      line: lineNumber,
      severity: 'info',
      message: 'Print statement found',
      suggestion: 'Consider using a logger instead of print statements'
    });
  }
  
  // Check for TODO comments
  const todoRegex = /#\s*TODO/g;
  
  while ((match = todoRegex.exec(content)) !== null) {
    const lineNumber = content.substring(0, match.index).split('\n').length;
    
    issues.push({
      file: filePath,
      line: lineNumber,
      severity: 'info',
      message: 'TODO comment found',
      suggestion: 'Consider addressing the TODO item'
    });
  }
  
  // Check for long lines
  lines.forEach((line, index) => {
    if (line.length > 79) {
      issues.push({
        file: filePath,
        line: index + 1,
        severity: 'info',
        message: 'Line exceeds 79 characters (PEP 8 recommendation)',
        suggestion: 'Consider breaking this line into multiple lines for better readability'
      });
    }
  });
  
  // Check for bare except clauses
  const bareExceptRegex = /except\s*:/g;
  
  while ((match = bareExceptRegex.exec(content)) !== null) {
    const lineNumber = content.substring(0, match.index).split('\n').length;
    
    issues.push({
      file: filePath,
      line: lineNumber,
      severity: 'warning',
      message: 'Bare except clause',
      suggestion: 'Specify the exceptions you want to catch'
    });
  }
}

/**
 * Identifies common issues in CSS files
 */
function identifyCSSIssues(content: string, filePath: string, issues: any[]) {
  // Check for !important
  const importantRegex = /!important/g;
  let match;
  
  while ((match = importantRegex.exec(content)) !== null) {
    const lineNumber = content.substring(0, match.index).split('\n').length;
    
    issues.push({
      file: filePath,
      line: lineNumber,
      severity: 'warning',
      message: '!important found',
      suggestion: 'Avoid using !important as it breaks the natural cascading of CSS'
    });
  }
  
  // Check for potential browser-specific prefixes without standard property
  const prefixRegex = /(-webkit-|-moz-|-ms-|-o-)[a-zA-Z-]+\s*:/g;
  
  while ((match = prefixRegex.exec(content)) !== null) {
    const lineNumber = content.substring(0, match.index).split('\n').length;
    const property = match[0].replace(':', '');
    const standardProperty = property.replace(/^(-webkit-|-moz-|-ms-|-o-)/, '');
    
    // Check if the standard property is also defined
    const standardRegex = new RegExp(`${standardProperty}\\s*:`, 'g');
    if (!standardRegex.test(content)) {
      issues.push({
        file: filePath,
        line: lineNumber,
        severity: 'info',
        message: `Vendor prefix ${property} used without standard property`,
        suggestion: `Consider adding the standard property ${standardProperty} as well`
      });
    }
  }
}