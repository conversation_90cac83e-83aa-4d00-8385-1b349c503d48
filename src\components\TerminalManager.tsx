import React, { useState, useRef, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, X, Maximize2, Terminal as TerminalIcon, Play, Trash2, RefreshCw } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import AdvancedTerminal, { AdvancedTerminalRef } from './AdvancedTerminal';
import { io } from 'socket.io-client';

interface TerminalTab {
  id: string;
  name: string;
  path?: string;
  terminalRef: React.RefObject<AdvancedTerminalRef>;
}

interface TerminalManagerProps {
  wsEndpoint?: string;
  defaultPath?: string;
  useWebGL?: boolean;
  allowImages?: boolean;
  useUnicode11?: boolean;
  showStatus?: boolean;
  autoReconnect?: boolean;
}

const TerminalManager: React.FC<TerminalManagerProps> = ({
  wsEndpoint = 'http://localhost:3002',
  defaultPath = '/',
  useWebGL = true,
  allowImages = true,
  useUnicode11 = true,
  showStatus = true,
  autoReconnect = true
}) => {
  const { toast } = useToast();
  const [tabs, setTabs] = useState<TerminalTab[]>([]);
  const [activeTab, setActiveTab] = useState<string | null>(null);
  const [isAddingTab, setIsAddingTab] = useState(false);
  const [newTabName, setNewTabName] = useState('Terminal');
  const [newTabPath, setNewTabPath] = useState(defaultPath);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isServerRunning, setIsServerRunning] = useState(false);
  const [serverStatus, setServerStatus] = useState('stopped');

  // Check if terminal server is running
  useEffect(() => {
    const checkServerStatus = async () => {
      try {
        // Try to connect to the server API
        const response = await fetch(`${wsEndpoint.replace(/^ws/, 'http')}/api/status`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          setIsServerRunning(true);
          setServerStatus('running');
          console.log('Terminal server status:', data);
        } else {
          setIsServerRunning(false);
          setServerStatus('stopped');
        }
      } catch (error) {
        // If API fails, try Socket.IO connection
        try {
          const socket = io(wsEndpoint, {
            transports: ['websocket'],
            reconnection: false,
            timeout: 2000
          });

          socket.on('connect', () => {
            setIsServerRunning(true);
            setServerStatus('running');
            socket.disconnect();
          });

          socket.on('connect_error', () => {
            setIsServerRunning(false);
            setServerStatus('stopped');
            socket.disconnect();
          });

          // Disconnect after 2 seconds if no connection
          setTimeout(() => {
            if (socket.connected) {
              socket.disconnect();
            }
          }, 2000);
        } catch (socketError) {
          setIsServerRunning(false);
          setServerStatus('stopped');
        }
      }
    };

    checkServerStatus();

    // Check server status every 5 seconds
    const interval = setInterval(checkServerStatus, 5000);

    return () => {
      clearInterval(interval);
    };
  }, [wsEndpoint]);

  // Create a default terminal tab on mount if server is running
  useEffect(() => {
    if (isServerRunning && tabs.length === 0) {
      handleAddTab();
    }
  }, [isServerRunning]);

  // Add a new terminal tab
  const handleAddTab = () => {
    if (!isAddingTab) {
      // Quick add with default values
      const id = `terminal-${Date.now()}`;
      const terminalRef = React.createRef<AdvancedTerminalRef>();

      const newTab: TerminalTab = {
        id,
        name: `Terminal ${tabs.length + 1}`,
        path: defaultPath,
        terminalRef
      };

      setTabs(prevTabs => [...prevTabs, newTab]);
      setActiveTab(id);

      toast({
        title: 'New Terminal',
        description: `Created new terminal: ${newTab.name}`,
      });
    } else {
      // Add with custom values from dialog
      const id = `terminal-${Date.now()}`;
      const terminalRef = React.createRef<AdvancedTerminalRef>();

      const newTab: TerminalTab = {
        id,
        name: newTabName || `Terminal ${tabs.length + 1}`,
        path: newTabPath || defaultPath,
        terminalRef
      };

      setTabs(prevTabs => [...prevTabs, newTab]);
      setActiveTab(id);
      setIsAddingTab(false);

      // Reset form
      setNewTabName('Terminal');
      setNewTabPath(defaultPath);

      toast({
        title: 'New Terminal',
        description: `Created new terminal: ${newTab.name}`,
      });
    }
  };

  // Close a terminal tab
  const handleCloseTab = (id: string) => {
    setTabs(prevTabs => prevTabs.filter(tab => tab.id !== id));

    // If the active tab is closed, activate another tab
    if (activeTab === id) {
      const remainingTabs = tabs.filter(tab => tab.id !== id);
      if (remainingTabs.length > 0) {
        setActiveTab(remainingTabs[0].id);
      } else {
        setActiveTab(null);
      }
    }
  };

  // Execute a command in the active terminal
  const executeCommand = (command: string) => {
    if (!activeTab) return;

    const tab = tabs.find(tab => tab.id === activeTab);
    if (tab && tab.terminalRef.current) {
      tab.terminalRef.current.executeCommand(command);
    }
  };

  // Start the terminal server
  const startTerminalServer = () => {
    // In a real implementation, this would start the server
    // For now, we'll just simulate it
    setServerStatus('starting');

    setTimeout(() => {
      setIsServerRunning(true);
      setServerStatus('running');

      toast({
        title: 'Terminal Server',
        description: 'Terminal server started successfully',
      });

      // Create a default terminal tab
      if (tabs.length === 0) {
        handleAddTab();
      }
    }, 1500);
  };

  // Stop the terminal server
  const stopTerminalServer = () => {
    // In a real implementation, this would stop the server
    // For now, we'll just simulate it
    setServerStatus('stopping');

    setTimeout(() => {
      setIsServerRunning(false);
      setServerStatus('stopped');

      toast({
        title: 'Terminal Server',
        description: 'Terminal server stopped',
      });
    }, 1500);
  };

  // Restart the terminal server
  const restartTerminalServer = () => {
    setServerStatus('restarting');

    setTimeout(() => {
      setIsServerRunning(true);
      setServerStatus('running');

      toast({
        title: 'Terminal Server',
        description: 'Terminal server restarted successfully',
      });
    }, 2000);
  };

  return (
    <div className={`flex flex-col ${isFullscreen ? 'fixed inset-0 z-50 bg-background' : 'h-full'}`}>
      {/* Terminal Manager Header */}
      <div className="flex items-center justify-between p-2 border-b border-border">
        <div className="flex items-center">
          <TerminalIcon className="h-5 w-5 mr-2 text-primary" />
          <h3 className="text-sm font-medium">Terminal Manager</h3>
        </div>

        <div className="flex items-center space-x-2">
          {/* Server Status */}
          <div className="flex items-center mr-4">
            <div className={`h-2 w-2 rounded-full mr-2 ${
              serverStatus === 'running' ? 'bg-green-500' :
              serverStatus === 'stopped' ? 'bg-red-500' :
              'bg-yellow-500 animate-pulse'
            }`} />
            <span className="text-xs text-muted-foreground capitalize">{serverStatus}</span>
          </div>

          {/* Server Controls */}
          {!isServerRunning && (
            <Button
              variant="outline"
              size="sm"
              onClick={startTerminalServer}
              disabled={serverStatus !== 'stopped'}
            >
              <Play className="h-4 w-4 mr-1" />
              Start Server
            </Button>
          )}

          {isServerRunning && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={stopTerminalServer}
                disabled={serverStatus !== 'running'}
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Stop Server
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={restartTerminalServer}
                disabled={serverStatus !== 'running'}
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                Restart
              </Button>
            </>
          )}

          {/* Add Tab Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsAddingTab(true)}
            disabled={!isServerRunning}
          >
            <Plus className="h-4 w-4 mr-1" />
            New Terminal
          </Button>

          {/* Fullscreen Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsFullscreen(!isFullscreen)}
          >
            <Maximize2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Terminal Tabs */}
      {tabs.length > 0 ? (
        <Tabs
          value={activeTab || undefined}
          onValueChange={setActiveTab}
          className="flex-1 flex flex-col"
        >
          <div className="border-b border-border overflow-x-auto">
            <TabsList className="bg-transparent h-9">
              {tabs.map(tab => (
                <div key={tab.id} className="flex items-center">
                  <TabsTrigger value={tab.id} className="relative px-4 py-1">
                    {tab.name}
                  </TabsTrigger>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-5 w-5 p-0 ml-1 mr-1 rounded-full"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCloseTab(tab.id);
                    }}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </TabsList>
          </div>

          {tabs.map(tab => (
            <TabsContent key={tab.id} value={tab.id} className="flex-1 p-0 m-0">
              <AdvancedTerminal
                ref={tab.terminalRef}
                wsEndpoint={wsEndpoint}
                defaultPath={tab.path}
                useWebGL={useWebGL}
                allowImages={allowImages}
                useUnicode11={useUnicode11}
                showStatus={showStatus}
                autoReconnect={autoReconnect}
                showProcessOutput={true}
              />
            </TabsContent>
          ))}
        </Tabs>
      ) : (
        <div className="flex-1 flex items-center justify-center flex-col p-8">
          {!isServerRunning ? (
            <Card className="w-full max-w-md">
              <CardHeader>
                <CardTitle>Terminal Server Not Running</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground">
                  The terminal server is not running. Start the server to use the terminal.
                </p>
                <Button onClick={startTerminalServer} className="w-full">
                  <Play className="h-4 w-4 mr-2" />
                  Start Terminal Server
                </Button>
              </CardContent>
            </Card>
          ) : (
            <Card className="w-full max-w-md">
              <CardHeader>
                <CardTitle>No Terminal Sessions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground">
                  You don't have any terminal sessions open. Create a new terminal to get started.
                </p>
                <Button onClick={handleAddTab} className="w-full">
                  <Plus className="h-4 w-4 mr-2" />
                  Create New Terminal
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Add Terminal Dialog */}
      <Dialog open={isAddingTab} onOpenChange={setIsAddingTab}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>New Terminal</DialogTitle>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={newTabName}
                onChange={(e) => setNewTabName(e.target.value)}
                className="col-span-3"
                placeholder="Terminal name"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="path" className="text-right">
                Path
              </Label>
              <Input
                id="path"
                value={newTabPath}
                onChange={(e) => setNewTabPath(e.target.value)}
                className="col-span-3"
                placeholder="Working directory"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddingTab(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddTab}>
              Create Terminal
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TerminalManager;
