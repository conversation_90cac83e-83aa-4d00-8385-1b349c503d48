/**
 * Web Search Service for finding code examples and documentation
 * This service integrates with external APIs to search for code examples,
 * documentation, and best practices.
 */

interface SearchResult {
  title: string;
  url: string;
  snippet: string;
  source: string;
  language?: string;
  code?: string;
  relevanceScore: number;
}

interface CodeExample {
  code: string;
  language: string;
  source: string;
  url: string;
  explanation?: string;
  author?: string;
  stars?: number;
  datePublished?: string;
}

interface SearchOptions {
  language?: string;
  framework?: string;
  maxResults?: number;
  includeCode?: boolean;
  includeDocumentation?: boolean;
  sortBy?: 'relevance' | 'date' | 'stars';
}

export class WebSearchService {
  private readonly API_ENDPOINT = '/api/web-search';
  private readonly GITHUB_API = 'https://api.github.com';
  private readonly STACKOVERFLOW_API = 'https://api.stackexchange.com/2.3';
  
  /**
   * Search for code examples and documentation
   */
  public async search(query: string, options: SearchOptions = {}): Promise<SearchResult[]> {
    try {
      // In a real implementation, this would call an external API
      // For now, we'll simulate the response
      
      // Add language and framework to the query if provided
      let enhancedQuery = query;
      if (options.language) {
        enhancedQuery += ` language:${options.language}`;
      }
      if (options.framework) {
        enhancedQuery += ` framework:${options.framework}`;
      }
      
      console.log(`Searching for: ${enhancedQuery}`);
      
      // Simulate API call
      const results = await this.simulateSearch(enhancedQuery, options);
      
      // Sort results based on options
      if (options.sortBy === 'date') {
        results.sort((a, b) => {
          const dateA = a.datePublished ? new Date(a.datePublished).getTime() : 0;
          const dateB = b.datePublished ? new Date(b.datePublished).getTime() : 0;
          return dateB - dateA; // Most recent first
        });
      } else if (options.sortBy === 'stars') {
        results.sort((a, b) => (b.stars || 0) - (a.stars || 0));
      } else {
        // Default sort by relevance
        results.sort((a, b) => b.relevanceScore - a.relevanceScore);
      }
      
      // Limit results
      const maxResults = options.maxResults || 10;
      return results.slice(0, maxResults);
    } catch (error) {
      console.error('Error searching for code examples:', error);
      throw new Error(`Failed to search for code examples: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  /**
   * Search for code examples on GitHub
   */
  public async searchGitHub(query: string, language?: string, maxResults: number = 5): Promise<CodeExample[]> {
    try {
      // In a real implementation, this would call the GitHub API
      // For now, we'll simulate the response
      
      console.log(`Searching GitHub for: ${query} in language: ${language || 'any'}`);
      
      // Simulate GitHub API call
      const results = await this.simulateGitHubSearch(query, language, maxResults);
      
      return results;
    } catch (error) {
      console.error('Error searching GitHub:', error);
      throw new Error(`Failed to search GitHub: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  /**
   * Search for answers on Stack Overflow
   */
  public async searchStackOverflow(query: string, maxResults: number = 5): Promise<SearchResult[]> {
    try {
      // In a real implementation, this would call the Stack Overflow API
      // For now, we'll simulate the response
      
      console.log(`Searching Stack Overflow for: ${query}`);
      
      // Simulate Stack Overflow API call
      const results = await this.simulateStackOverflowSearch(query, maxResults);
      
      return results;
    } catch (error) {
      console.error('Error searching Stack Overflow:', error);
      throw new Error(`Failed to search Stack Overflow: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  /**
   * Get documentation for a specific library or framework
   */
  public async getDocumentation(library: string, version?: string): Promise<SearchResult[]> {
    try {
      // In a real implementation, this would call an API to get documentation
      // For now, we'll simulate the response
      
      console.log(`Getting documentation for: ${library} version: ${version || 'latest'}`);
      
      // Simulate documentation API call
      const results = await this.simulateDocumentationSearch(library, version);
      
      return results;
    } catch (error) {
      console.error('Error getting documentation:', error);
      throw new Error(`Failed to get documentation: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  /**
   * Extract code examples from a web page
   */
  public async extractCodeFromUrl(url: string): Promise<CodeExample[]> {
    try {
      // In a real implementation, this would fetch the web page and extract code blocks
      // For now, we'll simulate the response
      
      console.log(`Extracting code from: ${url}`);
      
      // Simulate code extraction
      const results = await this.simulateCodeExtraction(url);
      
      return results;
    } catch (error) {
      console.error('Error extracting code from URL:', error);
      throw new Error(`Failed to extract code from URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  // Simulation methods for development
  
  private async simulateSearch(query: string, options: SearchOptions): Promise<SearchResult[]> {
    // Simulate a delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Generate some fake search results based on the query
    const results: SearchResult[] = [];
    
    // Add GitHub results
    if (options.includeCode !== false) {
      const githubResults = await this.simulateGitHubSearch(query, options.language, 3);
      
      results.push(...githubResults.map(result => ({
        title: `GitHub: ${result.source}`,
        url: result.url,
        snippet: result.explanation || 'Code example from GitHub',
        source: 'GitHub',
        language: result.language,
        code: result.code,
        relevanceScore: 0.8 + Math.random() * 0.2,
        stars: result.stars,
        datePublished: result.datePublished
      })));
    }
    
    // Add Stack Overflow results
    const stackoverflowResults = await this.simulateStackOverflowSearch(query, 3);
    results.push(...stackoverflowResults);
    
    // Add documentation results
    if (options.includeDocumentation !== false) {
      // Extract potential library names from the query
      const libraryMatch = query.match(/\b(react|angular|vue|node|express|django|flask|spring|laravel)\b/i);
      if (libraryMatch) {
        const library = libraryMatch[1].toLowerCase();
        const docResults = await this.simulateDocumentationSearch(library);
        results.push(...docResults);
      }
    }
    
    return results;
  }
  
  private async simulateGitHubSearch(query: string, language?: string, maxResults: number = 5): Promise<CodeExample[]> {
    // Simulate a delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Generate some fake GitHub results based on the query
    const results: CodeExample[] = [];
    
    // Common programming tasks with example code
    const examples: Record<string, { language: string; code: string; explanation: string }[]> = {
      'react component': [
        {
          language: 'jsx',
          code: `import React, { useState } from 'react';

function Counter() {
  const [count, setCount] = useState(0);
  
  return (
    <div>
      <p>You clicked {count} times</p>
      <button onClick={() => setCount(count + 1)}>
        Click me
      </button>
    </div>
  );
}

export default Counter;`,
          explanation: 'A simple React counter component using hooks'
        },
        {
          language: 'tsx',
          code: `import React, { useState } from 'react';

interface CounterProps {
  initialCount?: number;
  step?: number;
}

const Counter: React.FC<CounterProps> = ({ 
  initialCount = 0, 
  step = 1 
}) => {
  const [count, setCount] = useState(initialCount);
  
  const increment = () => setCount(count + step);
  const decrement = () => setCount(count - step);
  
  return (
    <div className="counter">
      <h2>Count: {count}</h2>
      <div className="buttons">
        <button onClick={decrement}>-</button>
        <button onClick={increment}>+</button>
      </div>
    </div>
  );
};

export default Counter;`,
          explanation: 'A TypeScript React counter component with props'
        }
      ],
      'express api': [
        {
          language: 'javascript',
          code: `const express = require('express');
const router = express.Router();

// Get all items
router.get('/api/items', async (req, res) => {
  try {
    const items = await Item.find();
    res.json(items);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
});

// Get one item
router.get('/api/items/:id', getItem, (req, res) => {
  res.json(res.item);
});

// Create item
router.post('/api/items', async (req, res) => {
  const item = new Item({
    name: req.body.name,
    description: req.body.description
  });
  
  try {
    const newItem = await item.save();
    res.status(201).json(newItem);
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
});

// Middleware to get item by ID
async function getItem(req, res, next) {
  let item;
  try {
    item = await Item.findById(req.params.id);
    if (item == null) {
      return res.status(404).json({ message: 'Item not found' });
    }
  } catch (err) {
    return res.status(500).json({ message: err.message });
  }
  
  res.item = item;
  next();
}

module.exports = router;`,
          explanation: 'Express.js REST API routes for CRUD operations'
        }
      ],
      'python flask': [
        {
          language: 'python',
          code: `from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_marshmallow import Marshmallow
import os

# Init app
app = Flask(__name__)

# Database
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///db.sqlite'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Init db
db = SQLAlchemy(app)

# Init ma
ma = Marshmallow(app)

# Product Model
class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True)
    description = db.Column(db.String(200))
    price = db.Column(db.Float)
    qty = db.Column(db.Integer)

    def __init__(self, name, description, price, qty):
        self.name = name
        self.description = description
        self.price = price
        self.qty = qty

# Product Schema
class ProductSchema(ma.Schema):
    class Meta:
        fields = ('id', 'name', 'description', 'price', 'qty')

# Init schema
product_schema = ProductSchema()
products_schema = ProductSchema(many=True)

# Create a Product
@app.route('/product', methods=['POST'])
def add_product():
    name = request.json['name']
    description = request.json['description']
    price = request.json['price']
    qty = request.json['qty']

    new_product = Product(name, description, price, qty)

    db.session.add(new_product)
    db.session.commit()

    return product_schema.jsonify(new_product)

# Get All Products
@app.route('/product', methods=['GET'])
def get_products():
    all_products = Product.query.all()
    result = products_schema.dump(all_products)
    return jsonify(result)

# Run Server
if __name__ == '__main__':
    app.run(debug=True)`,
          explanation: 'Flask API with SQLAlchemy and Marshmallow for data serialization'
        }
      ]
    };
    
    // Find matching examples
    for (const [key, exampleList] of Object.entries(examples)) {
      if (query.toLowerCase().includes(key.toLowerCase())) {
        // Filter by language if specified
        const filteredExamples = language
          ? exampleList.filter(ex => ex.language.toLowerCase().includes(language.toLowerCase()))
          : exampleList;
        
        for (const example of filteredExamples.slice(0, maxResults)) {
          results.push({
            code: example.code,
            language: example.language,
            source: `Example ${key} code`,
            url: `https://github.com/example/${key.replace(/\s+/g, '-')}`,
            explanation: example.explanation,
            author: 'GitHub User',
            stars: Math.floor(Math.random() * 1000) + 50,
            datePublished: new Date(Date.now() - Math.random() * 10000000000).toISOString()
          });
        }
      }
    }
    
    // If no specific examples match, generate generic ones
    if (results.length === 0) {
      const langs = language ? [language] : ['javascript', 'python', 'typescript'];
      const selectedLang = langs[Math.floor(Math.random() * langs.length)];
      
      if (selectedLang === 'javascript' || selectedLang === 'typescript') {
        results.push({
          code: `// Example function for ${query}
function process${query.replace(/\s+/g, '')}(data) {
  // Process the data
  const result = data.map(item => {
    return {
      ...item,
      processed: true,
      timestamp: new Date().toISOString()
    };
  });
  
  return result;
}

module.exports = process${query.replace(/\s+/g, '')};`,
          language: selectedLang,
          source: `Generic ${selectedLang} example`,
          url: `https://github.com/example/${query.replace(/\s+/g, '-')}`,
          explanation: `A generic ${selectedLang} example for ${query}`,
          stars: Math.floor(Math.random() * 100) + 10,
          datePublished: new Date(Date.now() - Math.random() * 10000000000).toISOString()
        });
      } else if (selectedLang === 'python') {
        results.push({
          code: `# Example function for ${query}
def process_${query.replace(/\s+/g, '_').toLowerCase()}(data):
    """
    Process data for ${query}
    
    Args:
        data: The input data to process
        
    Returns:
        The processed data
    """
    # Process the data
    result = []
    for item in data:
        item['processed'] = True
        item['timestamp'] = datetime.now().isoformat()
        result.append(item)
    
    return result`,
          language: 'python',
          source: 'Generic Python example',
          url: `https://github.com/example/${query.replace(/\s+/g, '-')}`,
          explanation: `A generic Python example for ${query}`,
          stars: Math.floor(Math.random() * 100) + 10,
          datePublished: new Date(Date.now() - Math.random() * 10000000000).toISOString()
        });
      }
    }
    
    return results;
  }
  
  private async simulateStackOverflowSearch(query: string, maxResults: number = 5): Promise<SearchResult[]> {
    // Simulate a delay
    await new Promise(resolve => setTimeout(resolve, 400));
    
    // Generate some fake Stack Overflow results based on the query
    const results: SearchResult[] = [];
    
    // Common programming questions with answers
    const answers: Record<string, { title: string; snippet: string; code?: string; language?: string }[]> = {
      'react hooks': [
        {
          title: 'How to use React useEffect hook correctly',
          snippet: 'The useEffect hook in React is used for side effects in function components. It\'s similar to componentDidMount, componentDidUpdate, and componentWillUnmount combined.',
          code: `useEffect(() => {
  // This runs after every render
  document.title = \`You clicked \${count} times\`;
  
  // Return a cleanup function
  return () => {
    // This runs before the next effect or on unmount
    document.title = 'React App';
  };
}, [count]); // Only re-run if count changes`,
          language: 'jsx'
        },
        {
          title: 'React useState hook not updating state',
          snippet: 'If your state isn\'t updating with useState, it might be because React state updates are asynchronous. You should use the functional form of setState when the new state depends on the previous state.',
          code: `// Wrong way
const increment = () => {
  setCount(count + 1);
  setCount(count + 1); // This will not work as expected
};

// Correct way
const increment = () => {
  setCount(prevCount => prevCount + 1);
  setCount(prevCount => prevCount + 1); // This will work correctly
};`,
          language: 'jsx'
        }
      ],
      'javascript async': [
        {
          title: 'How to use async/await in JavaScript',
          snippet: 'async/await is syntactic sugar on top of Promises, making asynchronous code easier to write and understand.',
          code: `async function fetchData() {
  try {
    const response = await fetch('https://api.example.com/data');
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
}

// Usage
fetchData()
  .then(data => console.log(data))
  .catch(error => console.error(error));`,
          language: 'javascript'
        }
      ],
      'python decorator': [
        {
          title: 'How to create and use decorators in Python',
          snippet: 'Decorators are a powerful feature in Python that allow you to modify the behavior of functions or classes without directly changing their source code.',
          code: `def timer_decorator(func):
    """A decorator that prints the execution time of a function"""
    import time
    
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"{func.__name__} executed in {end_time - start_time:.4f} seconds")
        return result
    
    return wrapper

@timer_decorator
def slow_function(n):
    """A slow function that demonstrates the decorator"""
    import time
    time.sleep(n)
    return n

# Usage
result = slow_function(2)  # Will print execution time`,
          language: 'python'
        }
      ]
    };
    
    // Find matching answers
    for (const [key, answerList] of Object.entries(answers)) {
      if (query.toLowerCase().includes(key.toLowerCase())) {
        for (const answer of answerList.slice(0, maxResults)) {
          results.push({
            title: answer.title,
            url: `https://stackoverflow.com/questions/${Math.floor(Math.random() * 10000000)}/${answer.title.toLowerCase().replace(/\s+/g, '-')}`,
            snippet: answer.snippet,
            source: 'Stack Overflow',
            language: answer.language,
            code: answer.code,
            relevanceScore: 0.7 + Math.random() * 0.3
          });
        }
      }
    }
    
    // If no specific answers match, generate generic ones
    if (results.length === 0) {
      results.push({
        title: `How to implement ${query} in JavaScript`,
        url: `https://stackoverflow.com/questions/${Math.floor(Math.random() * 10000000)}/how-to-implement-${query.toLowerCase().replace(/\s+/g, '-')}`,
        snippet: `I'm trying to implement ${query} in my project but I'm running into some issues. I've tried several approaches but none seem to work correctly.`,
        source: 'Stack Overflow',
        relevanceScore: 0.6 + Math.random() * 0.2
      });
      
      results.push({
        title: `Best practices for ${query}`,
        url: `https://stackoverflow.com/questions/${Math.floor(Math.random() * 10000000)}/best-practices-for-${query.toLowerCase().replace(/\s+/g, '-')}`,
        snippet: `What are the best practices for implementing ${query}? I want to make sure I'm following industry standards and avoiding common pitfalls.`,
        source: 'Stack Overflow',
        relevanceScore: 0.5 + Math.random() * 0.2
      });
    }
    
    return results;
  }
  
  private async simulateDocumentationSearch(library: string, version?: string): Promise<SearchResult[]> {
    // Simulate a delay
    await new Promise(resolve => setTimeout(resolve, 200));
    
    // Generate some fake documentation results based on the library
    const results: SearchResult[] = [];
    
    // Documentation for common libraries
    const docs: Record<string, { title: string; url: string; snippet: string }[]> = {
      'react': [
        {
          title: 'React Hooks API Reference',
          url: 'https://reactjs.org/docs/hooks-reference.html',
          snippet: 'Hooks are a new addition in React 16.8. They let you use state and other React features without writing a class.'
        },
        {
          title: 'React Component API',
          url: 'https://reactjs.org/docs/react-component.html',
          snippet: 'This page contains a detailed API reference for the React component class definition.'
        },
        {
          title: 'React Context API',
          url: 'https://reactjs.org/docs/context.html',
          snippet: 'Context provides a way to pass data through the component tree without having to pass props down manually at every level.'
        }
      ],
      'angular': [
        {
          title: 'Angular Components Overview',
          url: 'https://angular.io/guide/component-overview',
          snippet: 'Components are the main building block for Angular applications. Each component consists of a TypeScript class, an HTML template, and styles.'
        },
        {
          title: 'Angular Services',
          url: 'https://angular.io/guide/architecture-services',
          snippet: 'Service is a broad category encompassing any value, function, or feature that an application needs.'
        }
      ],
      'vue': [
        {
          title: 'Vue.js Guide',
          url: 'https://vuejs.org/v2/guide/',
          snippet: 'Vue is a progressive framework for building user interfaces. Unlike other monolithic frameworks, Vue is designed from the ground up to be incrementally adoptable.'
        },
        {
          title: 'Vue.js Components',
          url: 'https://vuejs.org/v2/guide/components.html',
          snippet: 'Components are one of the most powerful features of Vue. They help you extend basic HTML elements to encapsulate reusable code.'
        }
      ],
      'node': [
        {
          title: 'Node.js Documentation',
          url: 'https://nodejs.org/en/docs/',
          snippet: 'Node.js® is a JavaScript runtime built on Chrome\'s V8 JavaScript engine.'
        },
        {
          title: 'Node.js File System',
          url: 'https://nodejs.org/api/fs.html',
          snippet: 'The fs module provides an API for interacting with the file system in a manner closely modeled around standard POSIX functions.'
        }
      ],
      'express': [
        {
          title: 'Express.js Guide',
          url: 'https://expressjs.com/en/guide/routing.html',
          snippet: 'Routing refers to how an application\'s endpoints (URIs) respond to client requests.'
        },
        {
          title: 'Express.js Middleware',
          url: 'https://expressjs.com/en/guide/using-middleware.html',
          snippet: 'Middleware functions are functions that have access to the request object (req), the response object (res), and the next middleware function in the application\'s request-response cycle.'
        }
      ]
    };
    
    // Find matching documentation
    if (library in docs) {
      for (const doc of docs[library]) {
        results.push({
          title: doc.title,
          url: doc.url,
          snippet: doc.snippet,
          source: `${library.charAt(0).toUpperCase() + library.slice(1)} Documentation`,
          relevanceScore: 0.9 + Math.random() * 0.1
        });
      }
    } else {
      // Generic documentation result
      results.push({
        title: `${library} Documentation`,
        url: `https://${library.toLowerCase()}.dev/docs`,
        snippet: `Official documentation for ${library}. Learn how to use ${library} in your projects.`,
        source: 'Documentation',
        relevanceScore: 0.7 + Math.random() * 0.2
      });
    }
    
    return results;
  }
  
  private async simulateCodeExtraction(url: string): Promise<CodeExample[]> {
    // Simulate a delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Generate some fake code examples based on the URL
    const results: CodeExample[] = [];
    
    // Extract domain from URL
    const domain = new URL(url).hostname;
    
    if (domain.includes('github.com')) {
      // Simulate GitHub code extraction
      results.push({
        code: `// Example code extracted from ${url}
function exampleFunction() {
  console.log('This is an example function extracted from a GitHub repository');
  return {
    status: 'success',
    message: 'Code extracted successfully'
  };
}`,
        language: 'javascript',
        source: 'GitHub',
        url: url
      });
    } else if (domain.includes('stackoverflow.com')) {
      // Simulate Stack Overflow code extraction
      results.push({
        code: `// Example code extracted from Stack Overflow
function bestPracticeExample() {
  // This follows best practices
  const result = [];
  
  // Process some data
  for (let i = 0; i < 10; i++) {
    result.push({
      id: i,
      value: i * 2
    });
  }
  
  return result;
}`,
        language: 'javascript',
        source: 'Stack Overflow',
        url: url,
        explanation: 'This code demonstrates best practices for processing data in JavaScript.'
      });
    } else {
      // Generic code extraction
      results.push({
        code: `// Example code extracted from ${domain}
// This is a generic code example
function genericExample() {
  return 'Hello, World!';
}`,
        language: 'javascript',
        source: domain,
        url: url
      });
    }
    
    return results;
  }
}

// Export a singleton instance
export const webSearchService = new WebSearchService();