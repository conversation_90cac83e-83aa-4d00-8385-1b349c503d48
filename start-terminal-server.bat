@echo off
echo ===================================
echo Advanced Terminal Server v2.0
echo ===================================
echo.
echo This script will install and start the advanced terminal server.
echo The server provides real terminal emulation with full interactivity.
echo.
echo Requirements:
echo - Node.js v14 or higher
echo - npm
echo - Windows Build Tools (for node-pty)
echo.
echo Press any key to continue or Ctrl+C to cancel...
pause > nul

echo.
echo Installing dependencies...
cd server
call npm install

echo.
echo Starting terminal server...
echo.
echo The terminal server is now running on http://localhost:3001
echo.
echo To use the terminal:
echo 1. Open the application in your browser
echo 2. Go to the Terminal tab
echo 3. Select "Advanced Terminal" or "Terminal Manager"
echo.
echo Press Ctrl+C to stop the server when you're done.
echo.
call npm start
