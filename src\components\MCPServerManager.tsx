
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Trash2, Plus, RefreshCw } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface ServerConfig {
  id: string;
  name: string;
  host: string;
  port: number;
  isOnline: boolean;
}

const MCPServerManager: React.FC = () => {
  const { toast } = useToast();
  const [servers, setServers] = useState<ServerConfig[]>([]);
  const [isAddingServer, setIsAddingServer] = useState(false);
  const [newServer, setNewServer] = useState({ name: '', host: '', port: 11434 });
  const [isCheckingStatus, setIsCheckingStatus] = useState(false);
  
  // Load servers from localStorage on component mount
  useEffect(() => {
    const savedServers = localStorage.getItem('mcp-servers');
    if (savedServers) {
      setServers(JSON.parse(savedServers));
    } else {
      // Default server if none exists
      const defaultServer = { 
        id: '1', 
        name: 'Local Server', 
        host: 'localhost', 
        port: 11434, // Ollama'nın varsayılan portu
        isOnline: false 
      };
      setServers([defaultServer]);
      localStorage.setItem('mcp-servers', JSON.stringify([defaultServer]));
    }
  }, []);
  
  // Save servers to localStorage whenever they change
  useEffect(() => {
    if (servers.length > 0) {
      localStorage.setItem('mcp-servers', JSON.stringify(servers));
    }
  }, [servers]);
  
  const handleAddServer = () => {
    if (!newServer.name || !newServer.host) {
      toast({
        title: "Validation Error",
        description: "Server name and host are required",
        variant: "destructive",
      });
      return;
    }
    
    const id = Date.now().toString();
    const updatedServers = [...servers, { 
      id, 
      name: newServer.name, 
      host: newServer.host, 
      port: newServer.port, 
      isOnline: false 
    }];
    
    setServers(updatedServers);
    localStorage.setItem('mcp-servers', JSON.stringify(updatedServers));
    
    setNewServer({ name: '', host: '', port: 11434 });
    setIsAddingServer(false);
    
    toast({
      title: "Server Added",
      description: `${newServer.name} has been added to your servers list`,
    });
  };
  
  const handleDeleteServer = (id: string) => {
    const updatedServers = servers.filter(server => server.id !== id);
    setServers(updatedServers);
    localStorage.setItem('mcp-servers', JSON.stringify(updatedServers));
    
    toast({
      title: "Server Removed",
      description: "The server has been removed from your list",
    });
  };
  
  const handleCheckStatus = async () => {
    setIsCheckingStatus(true);
    
    try {
      // Create a copy of the servers array to update
      const updatedServers = [...servers];
      
      // Check each server's status
      for (let i = 0; i < updatedServers.length; i++) {
        const server = updatedServers[i];
        try {
          // Use proxy for local servers to avoid CORS issues
          const isLocalhost = server.host === 'localhost' || server.host === '127.0.0.1';
          
          // Construct the URL based on whether it's a local server or not
          let url;
          if (isLocalhost) {
            url = `/ollama-api/api/tags`; // Use proxy path for local servers
          } else {
            // For remote servers, we'll need to handle CORS differently
            // This might not work directly in the browser due to CORS
            url = `http://${server.host}:${server.port}/api/tags`;
          }
          
          console.log(`Checking server ${server.name} at ${url}`);
          
          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
            signal: AbortSignal.timeout(5000) // Longer timeout
          });
          
          if (response.ok) {
            server.isOnline = true;
            const data = await response.json();
            console.log(`Server ${server.name} is online. Models:`, data);
          } else {
            console.log(`Server ${server.name} returned status: ${response.status}`);
            server.isOnline = false;
          }
        } catch (error) {
          console.error(`Error checking server ${server.name}:`, error);
          server.isOnline = false;
        }
      }
      
      // Update state and localStorage
      setServers(updatedServers);
      localStorage.setItem('mcp-servers', JSON.stringify(updatedServers));
      
      toast({
        title: "Status Check Complete",
        description: "All server statuses have been updated",
      });
    } catch (error) {
      console.error("Status check error:", error);
      toast({
        title: "Status Check Failed",
        description: "Failed to check server status",
        variant: "destructive",
      });
    } finally {
      setIsCheckingStatus(false);
    }
  };
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">MCP Server Manager</h3>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setIsAddingServer(true)}
            disabled={isAddingServer}
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Server
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={handleCheckStatus}
            disabled={isCheckingStatus}
          >
            <RefreshCw className={`h-4 w-4 mr-1 ${isCheckingStatus ? 'animate-spin' : ''}`} />
            Check Status
          </Button>
        </div>
      </div>
      
      {isAddingServer && (
        <div className="bg-secondary/30 p-3 rounded-md space-y-3">
          <div className="grid grid-cols-2 gap-2">
            <Input 
              placeholder="Server Name" 
              value={newServer.name}
              onChange={(e) => setNewServer({...newServer, name: e.target.value})}
            />
            <Input 
              placeholder="Host (e.g. localhost)" 
              value={newServer.host}
              onChange={(e) => setNewServer({...newServer, host: e.target.value})}
            />
          </div>
          <div className="grid grid-cols-2 gap-2">
            <Input 
              type="number" 
              placeholder="Port" 
              value={newServer.port}
              onChange={(e) => setNewServer({...newServer, port: parseInt(e.target.value) || 11434})}
            />
            <div className="flex gap-2">
              <Button 
                variant="default" 
                size="sm" 
                className="flex-1"
                onClick={handleAddServer}
              >
                Save
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="flex-1"
                onClick={() => {
                  setIsAddingServer(false);
                  setNewServer({ name: '', host: '', port: 11434 });
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}
      
      <div className="space-y-2">
        {servers.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <p>No servers added yet</p>
            <p className="text-sm">Click "Add Server" to add your first MCP server</p>
          </div>
        ) : (
          servers.map(server => (
            <div 
              key={server.id}
              className="flex items-center justify-between bg-secondary/50 p-3 rounded-md"
            >
              <div className="flex flex-col">
                <div className="font-medium text-foreground flex items-center">
                  {server.name}
                  <Badge 
                    variant={server.isOnline ? "default" : "destructive"} 
                    className="ml-2 text-xs"
                  >
                    {server.isOnline ? 'Online' : 'Offline'}
                  </Badge>
                </div>
                <div className="text-xs text-muted-foreground">
                  {server.host}:{server.port}
                </div>
              </div>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => handleDeleteServer(server.id)}
              >
                <Trash2 className="h-4 w-4 text-muted-foreground hover:text-destructive" />
              </Button>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default MCPServerManager;
