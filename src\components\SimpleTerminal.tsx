import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Terminal, Play, Square, Trash2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TerminalLine {
  id: string;
  type: 'command' | 'output' | 'error';
  content: string;
  timestamp: Date;
}

interface SimpleTerminalProps {
  onExecuteCommand?: (command: string) => Promise<string>;
  className?: string;
}

const SimpleTerminal: React.FC<SimpleTerminalProps> = ({ 
  onExecuteCommand,
  className 
}) => {
  const [lines, setLines] = useState<TerminalLine[]>([
    {
      id: 'welcome',
      type: 'output',
      content: 'Terminal hazır. Komutlarınızı yazabilirsiniz.',
      timestamp: new Date()
    }
  ]);
  const [currentCommand, setCurrentCommand] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);

  // Auto scroll to bottom
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [lines]);

  const addLine = (type: TerminalLine['type'], content: string) => {
    const newLine: TerminalLine = {
      id: `line-${Date.now()}-${Math.random()}`,
      type,
      content,
      timestamp: new Date()
    };
    setLines(prev => [...prev, newLine]);
  };

  const executeCommand = async (command: string) => {
    if (!command.trim()) return;

    // Add command to terminal
    addLine('command', `$ ${command}`);
    setCurrentCommand('');
    setIsExecuting(true);

    try {
      let output = '';

      // Built-in commands
      if (command.trim() === 'clear') {
        setLines([]);
        setIsExecuting(false);
        return;
      }

      if (command.trim() === 'help') {
        output = `Kullanılabilir komutlar:
  clear         - Terminali temizle
  help          - Bu yardım mesajını göster
  date          - Geçerli tarih ve saati göster
  pwd           - Geçerli dizini göster
  ls            - Dosyaları listele
  echo <text>   - Metni yazdır

NPM komutları:
  npm install   - Bağımlılıkları yükle
  npm run dev   - Development server başlat
  npm run build - Production build oluştur
  npm test      - Testleri çalıştır

Git komutları:
  git status    - Repository durumunu göster
  git add .     - Değişiklikleri stage'e ekle
  git commit    - Commit oluştur

Özel komutlar için AI asistanını kullanabilirsiniz.`;
      } else if (command.trim() === 'date') {
        output = new Date().toLocaleString('tr-TR');
      } else if (command.trim() === 'pwd') {
        output = '/workspace/ai-code-scribe-engine';
      } else if (command.trim().startsWith('echo ')) {
        output = command.slice(5);
      } else if (command.trim() === 'ls') {
        output = `src/
components/
pages/
services/
package.json
README.md
tsconfig.json
vite.config.ts
node_modules/`;
      } else if (command.trim().startsWith('npm ')) {
        const npmCommand = command.slice(4);
        if (npmCommand === 'install' || npmCommand === 'i') {
          output = `npm install başlatılıyor...
Bağımlılıklar yükleniyor...
✓ Tüm paketler başarıyla yüklendi.`;
        } else if (npmCommand === 'run dev') {
          output = `Development server başlatılıyor...
✓ Server http://localhost:8080 adresinde çalışıyor`;
        } else if (npmCommand === 'run build') {
          output = `Production build başlatılıyor...
✓ Build başarıyla tamamlandı.`;
        } else if (npmCommand === 'test') {
          output = `Test suite çalıştırılıyor...
✓ Tüm testler başarılı.`;
        } else {
          output = `npm ${npmCommand} komutu çalıştırıldı.`;
        }
      } else if (command.trim().startsWith('git ')) {
        const gitCommand = command.slice(4);
        if (gitCommand === 'status') {
          output = `On branch main
Your branch is up to date with 'origin/main'.

Changes not staged for commit:
  modified:   src/components/ChatInterface.tsx
  modified:   src/components/SimpleTerminal.tsx`;
        } else if (gitCommand.startsWith('add')) {
          output = `Dosyalar staging area'ya eklendi.`;
        } else if (gitCommand.startsWith('commit')) {
          output = `Commit başarıyla oluşturuldu.`;
        } else {
          output = `git ${gitCommand} komutu çalıştırıldı.`;
        }
      } else if (onExecuteCommand) {
        // Use custom command handler
        output = await onExecuteCommand(command);
      } else {
        // Simulate command execution
        await new Promise(resolve => setTimeout(resolve, 500));
        output = `Komut çalıştırıldı: ${command}`;
      }

      addLine('output', output);
    } catch (error) {
      addLine('error', `Hata: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`);
    } finally {
      setIsExecuting(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isExecuting) {
      executeCommand(currentCommand);
    }
  };

  const clearTerminal = () => {
    setLines([]);
  };

  const renderLine = (line: TerminalLine) => {
    const timeStr = line.timestamp.toLocaleTimeString();
    
    return (
      <div key={line.id} className="font-mono text-sm">
        {line.type === 'command' && (
          <div className="text-green-400 flex items-center gap-2">
            <span className="text-gray-500 text-xs">{timeStr}</span>
            <span>{line.content}</span>
          </div>
        )}
        {line.type === 'output' && (
          <div className="text-gray-300 ml-16 whitespace-pre-wrap">
            {line.content}
          </div>
        )}
        {line.type === 'error' && (
          <div className="text-red-400 ml-16 whitespace-pre-wrap">
            {line.content}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={cn("flex flex-col h-full bg-black text-white rounded-lg overflow-hidden", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-700 bg-gray-900">
        <div className="flex items-center gap-2">
          <Terminal className="h-4 w-4" />
          <span className="text-sm font-medium">Terminal</span>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={clearTerminal}
            className="h-7 px-2 text-gray-400 hover:text-white"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Terminal Content */}
      <div className="flex-1 flex flex-col">
        <ScrollArea className="flex-1 p-4" ref={scrollRef}>
          <div className="space-y-1">
            {lines.map(renderLine)}
            {isExecuting && (
              <div className="text-yellow-400 font-mono text-sm flex items-center gap-2">
                <div className="animate-spin">⟳</div>
                <span>Çalıştırılıyor...</span>
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Command Input */}
        <div className="border-t border-gray-700 p-3 bg-gray-900">
          <div className="flex items-center gap-2">
            <span className="text-green-400 font-mono text-sm">$</span>
            <Input
              value={currentCommand}
              onChange={(e) => setCurrentCommand(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Komut yazın..."
              className="flex-1 bg-transparent border-none text-white font-mono text-sm focus:ring-0 focus:ring-offset-0"
              disabled={isExecuting}
            />
            <Button
              onClick={() => executeCommand(currentCommand)}
              disabled={!currentCommand.trim() || isExecuting}
              size="sm"
              className="h-8"
            >
              {isExecuting ? (
                <Square className="h-3 w-3" />
              ) : (
                <Play className="h-3 w-3" />
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleTerminal;
