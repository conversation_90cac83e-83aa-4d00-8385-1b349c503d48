
import React, { useState, useRef, useImperativeHandle, forwardRef, useEffect } from 'react';
import { Terminal as XTerm } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import 'xterm/css/xterm.css';

export interface TerminalRef {
  executeCommand: (command: string) => void;
  focus: () => void;
  clear: () => void;
  write: (data: string) => void;
}

const Terminal = forwardRef<TerminalRef, {}>((props, ref) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const xtermRef = useRef<XTerm | null>(null);
  const fitAddonRef = useRef<FitAddon | null>(null);
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [currentInput, setCurrentInput] = useState('');
  const [cursorPosition, setCursorPosition] = useState(0);
  const [serverProcess, setServerProcess] = useState<any>(null);
  
  // Terminal başlatma
  useEffect(() => {
    if (!terminalRef.current) return;
    
    // XTerm.js kurulumu
    const term = new XTerm({
      cursorBlink: true,
      fontFamily: 'Menlo, Monaco, "Courier New", monospace',
      fontSize: 14,
      theme: {
        background: '#1a1b26',
        foreground: '#c0caf5',
        cursor: '#c0caf5',
        black: '#15161E',
        red: '#f7768e',
        green: '#9ece6a',
        yellow: '#e0af68',
        blue: '#7aa2f7',
        magenta: '#bb9af7',
        cyan: '#7dcfff',
        white: '#a9b1d6',
        brightBlack: '#414868',
        brightRed: '#f7768e',
        brightGreen: '#9ece6a',
        brightYellow: '#e0af68',
        brightBlue: '#7aa2f7',
        brightMagenta: '#bb9af7',
        brightCyan: '#7dcfff',
        brightWhite: '#c0caf5'
      }
    });
    
    const fitAddon = new FitAddon();
    term.loadAddon(fitAddon);
    
    term.open(terminalRef.current);
    fitAddon.fit();
    
    xtermRef.current = term;
    fitAddonRef.current = fitAddon;
    
    // Terminal başlangıç mesajı
    term.writeln('\x1b[1;34m# Ollama Agent Terminal\x1b[0m');
    term.writeln('\x1b[1;34m# Komutları buradan çalıştırabilirsiniz\x1b[0m');
    term.writeln('');
    term.write('\x1b[32m$ \x1b[0m');
    
    // Pencere boyutu değiştiğinde terminal boyutunu ayarla
    const handleResize = () => {
      if (fitAddonRef.current) {
        fitAddonRef.current.fit();
      }
    };
    
    window.addEventListener('resize', handleResize);
    
    // Klavye olaylarını dinle
    term.onKey(({ key, domEvent }) => {
      const ev = domEvent;
      const printable = !ev.altKey && !ev.ctrlKey && !ev.metaKey;
      
      if (ev.keyCode === 13) { // Enter
        const command = currentInput.trim();
        if (command) {
          term.write('\r\n');
          executeCommand(command);
          setCommandHistory(prev => [command, ...prev.slice(0, 49)]);
          setHistoryIndex(-1);
        } else {
          term.write('\r\n\x1b[32m$ \x1b[0m');
        }
        setCurrentInput('');
        setCursorPosition(0);
      } else if (ev.keyCode === 8) { // Backspace
        if (cursorPosition > 0) {
          const newInput = currentInput.substring(0, cursorPosition - 1) + 
                          currentInput.substring(cursorPosition);
          setCurrentInput(newInput);
          setCursorPosition(prev => prev - 1);
          
          // Terminal ekranını güncelle
          term.write('\b \b');
        }
      } else if (ev.keyCode === 38) { // Up arrow
        if (commandHistory.length > 0 && historyIndex < commandHistory.length - 1) {
          const newIndex = historyIndex + 1;
          setHistoryIndex(newIndex);
          const command = commandHistory[newIndex];
          
          // Mevcut satırı temizle
          term.write('\r\x1b[K\x1b[32m$ \x1b[0m' + command);
          setCurrentInput(command);
          setCursorPosition(command.length);
        }
      } else if (ev.keyCode === 40) { // Down arrow
        if (historyIndex > 0) {
          const newIndex = historyIndex - 1;
          setHistoryIndex(newIndex);
          const command = commandHistory[newIndex];
          
          // Mevcut satırı temizle
          term.write('\r\x1b[K\x1b[32m$ \x1b[0m' + command);
          setCurrentInput(command);
          setCursorPosition(command.length);
        } else if (historyIndex === 0) {
          setHistoryIndex(-1);
          term.write('\r\x1b[K\x1b[32m$ \x1b[0m');
          setCurrentInput('');
          setCursorPosition(0);
        }
      } else if (printable) {
        const newInput = currentInput.substring(0, cursorPosition) + 
                        key + 
                        currentInput.substring(cursorPosition);
        setCurrentInput(newInput);
        setCursorPosition(prev => prev + 1);
        term.write(key);
      }
    });
    
    return () => {
      window.removeEventListener('resize', handleResize);
      term.dispose();
      
      // Eğer bir sunucu çalışıyorsa, kapatın
      if (serverProcess) {
        try {
          serverProcess.kill();
        } catch (error) {
          console.error('Failed to kill server process:', error);
        }
      }
    };
  }, []);
  
  // Gerçek bir sunucu başlatma fonksiyonu
  const startServer = (filePath: string) => {
    if (!xtermRef.current) return;
    
    const term = xtermRef.current;
    
    // Önce mevcut sunucuyu kapat
    if (serverProcess) {
      try {
        serverProcess.kill();
        setServerProcess(null);
      } catch (error) {
        console.error('Failed to kill previous server:', error);
      }
    }
    
    // Yeni bir pencere aç
    const serverWindow = window.open('http://localhost:3000', '_blank');
    
    if (serverWindow) {
      term.writeln('\x1b[32m# Sunucu başlatıldı: http://localhost:3000\x1b[0m');
      term.writeln('\x1b[32m# Yeni pencerede açıldı\x1b[0m');
    } else {
      term.writeln('\x1b[31m# Popup penceresi engellenmiş olabilir. Lütfen tarayıcı ayarlarınızı kontrol edin.\x1b[0m');
      term.writeln('\x1b[32m# Sunucu şu adreste çalışıyor: http://localhost:3000\x1b[0m');
    }
    
    // Sunucu çıktısını simüle et
    term.writeln('Server running at http://localhost:3000');
    term.writeln('Press Ctrl+C to stop the server');
  };
  
  // Komut çalıştırma fonksiyonu
  const executeCommand = (command: string) => {
    if (!xtermRef.current) return;
    
    const term = xtermRef.current;
    
    // Komut çalıştırma simülasyonu
    if (command.startsWith('node ')) {
      const parts = command.split(' ');
      const filePath = parts[1];
      
      term.writeln(`Executing: ${command}`);
      term.writeln('\x1b[32m# JavaScript dosyası çalıştırılıyor...\x1b[0m');
      
      // Sunucu başlat
      if (filePath === 'server.js') {
        startServer(filePath);
      } else {
        term.writeln('Server started at http://localhost:3000');
        term.writeln('Listening for connections...');
      }
      
      term.write('\r\n\x1b[32m$ \x1b[0m');
    } else if (command.startsWith('ts-node ')) {
      const parts = command.split(' ');
      const filePath = parts[1];
      
      term.writeln(`Executing: ${command}`);
      term.writeln('\x1b[32m# TypeScript dosyası çalıştırılıyor...\x1b[0m');
      
      // Sunucu başlat
      if (filePath.includes('server')) {
        startServer(filePath);
      } else {
        term.writeln('TypeScript compilation successful');
        term.writeln('Server started at http://localhost:3000');
      }
      
      term.write('\r\n\x1b[32m$ \x1b[0m');
    } else if (command.startsWith('python ')) {
      term.writeln(`Executing: ${command}`);
      term.writeln('\x1b[32m# Python dosyası çalıştırılıyor...\x1b[0m');
      term.writeln('Python 3.9.5');
      term.writeln('Hello from Python!');
      term.write('\r\n\x1b[32m$ \x1b[0m');
    } else if (command.startsWith('npm ')) {
      const parts = command.split(' ');
      const subCommand = parts[1];
      
      term.writeln(`Executing: ${command}`);
      
      if (subCommand === 'start') {
        term.writeln('\x1b[32m# npm start çalıştırılıyor...\x1b[0m');
        term.writeln('> starting development server...');
        term.writeln('> webpack compiled successfully');
        
        // Sunucu başlat
        startServer('server.js');
      } else if (subCommand === 'install') {
        term.writeln('\x1b[32m# npm install çalıştırılıyor...\x1b[0m');
        term.writeln('> added 1425 packages in 25s');
        term.writeln('> found 0 vulnerabilities');
        term.write('\r\n\x1b[32m$ \x1b[0m');
      } else {
        term.writeln(`Running npm ${parts.slice(1).join(' ')}...`);
        term.writeln('> completed successfully');
        term.write('\r\n\x1b[32m$ \x1b[0m');
      }
    } else if (command === 'clear' || command === 'cls') {
      term.clear();
      term.write('\x1b[32m$ \x1b[0m');
    } else if (command === 'help') {
      term.writeln('\x1b[1;34m# Kullanılabilir komutlar:\x1b[0m');
      term.writeln('  node <file.js>     - JavaScript dosyasını çalıştır');
      term.writeln('  ts-node <file.ts>  - TypeScript dosyasını çalıştır');
      term.writeln('  python <file.py>   - Python dosyasını çalıştır');
      term.writeln('  npm start          - Geliştirme sunucusunu başlat');
      term.writeln('  npm install        - Bağımlılıkları yükle');
      term.writeln('  clear              - Terminali temizle');
      term.writeln('  help               - Bu yardım mesajını göster');
      term.write('\r\n\x1b[32m$ \x1b[0m');
    } else if (command === 'open http://localhost:3000') {
      term.writeln('Opening http://localhost:3000 in a new tab...');
      window.open('http://localhost:3000', '_blank');
      term.write('\r\n\x1b[32m$ \x1b[0m');
    } else {
      term.writeln(`\x1b[31mCommand not found: ${command}\x1b[0m`);
      term.writeln('Type "help" for available commands');
      term.write('\x1b[32m$ \x1b[0m');
    }
  };
  
  // Terminal referansını dışa aktar
  useImperativeHandle(ref, () => ({
    executeCommand: (command: string) => {
      if (xtermRef.current) {
        xtermRef.current.write(`\r\n\x1b[32m$ \x1b[0m${command}\r\n`);
        executeCommand(command);
      }
    },
    focus: () => {
      if (xtermRef.current) {
        xtermRef.current.focus();
      }
    },
    clear: () => {
      if (xtermRef.current) {
        xtermRef.current.clear();
        xtermRef.current.write('\x1b[32m$ \x1b[0m');
      }
    },
    write: (data: string) => {
      if (xtermRef.current) {
        xtermRef.current.write(data);
      }
    }
  }));
  
  return (
    <div className="h-full w-full overflow-hidden rounded-md border border-border bg-black">
      <div ref={terminalRef} className="h-full w-full" />
    </div>
  );
});

Terminal.displayName = 'Terminal';

export default Terminal;
