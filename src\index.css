
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600;700&family=Plus+Jakarta+Sans:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 222 47% 13%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 210 40% 98%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 261.2 73.4% 66.7%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 60.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;

    --radius: 0.5rem;

    --sidebar-background: 222 47% 9%;
    --sidebar-foreground: 210 40% 90%;
    --sidebar-primary: 217.2 91.2% 59.8%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 261.2 73.4% 66.7%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 222 47% 15%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }
  
  h1, h2, h3, h4 {
    @apply font-sans font-medium;
  }
}

.code-editor {
  font-family: 'JetBrains Mono', monospace;
}

.code-line {
  @apply py-0.5 hover:bg-muted/50 transition-colors duration-150;
}

.code-line-number {
  @apply text-muted-foreground text-right pr-2 select-none;
}

.code-content {
  @apply text-code-foreground;
}

.code-content .keyword {
  @apply text-code-keyword;
}

.code-content .string {
  @apply text-code-string;
}

.code-content .comment {
  @apply text-code-comment;
}

.code-content .function {
  @apply text-code-function;
}

.code-content .variable {
  @apply text-code-variable;
}

.code-content .number {
  @apply text-code-number;
}

.code-content .type {
  @apply text-code-type;
}

/* Monaco Editor Styling */
.monaco-editor-container {
  @apply h-full w-full;
}

.monaco-editor .margin,
.monaco-editor-background {
  background-color: var(--background);
}

/* Terminal Styling */
.terminal-container {
  @apply bg-code min-h-[200px];
}

.xterm .xterm-viewport,
.xterm-screen {
  @apply bg-code;
}

/* Modern UI elements */
.glass-card {
  @apply bg-card/80 backdrop-blur-sm border border-white/10 rounded-lg shadow-lg;
}

.modern-layout {
  @apply bg-gradient-to-br from-background to-background/80;
}

.modern-input {
  @apply bg-muted/30 border-0 focus:ring-1 focus:ring-primary/50 rounded-md;
}

.modern-button {
  @apply rounded-md transition-all duration-200 hover:translate-y-[-2px] active:translate-y-[0px];
}

.tooltip {
  @apply bg-popover text-popover-foreground text-xs py-1 px-2 rounded shadow-lg;
}

/* Font Animations */
@keyframes gradient-text {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-text {
  @apply text-transparent bg-clip-text;
  background-image: linear-gradient(90deg, var(--primary), var(--accent), var(--primary));
  background-size: 200% auto;
  animation: gradient-text 3s ease infinite;
}

/* Markdown content styling */
.markdown-content {
  font-size: 0.95rem;
  line-height: 1.6;
}

.markdown-content h1 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  color: hsl(var(--primary));
}

.markdown-content h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
  color: hsl(var(--primary));
}

.markdown-content h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

.markdown-content pre {
  background-color: hsl(var(--muted));
  border-radius: 0.375rem;
  padding: 1rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.markdown-content code {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 0.9em;
  background-color: hsl(var(--muted));
  padding: 0.2em 0.4em;
  border-radius: 0.25rem;
}

.markdown-content pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

.markdown-content li {
  margin-bottom: 0.25rem;
  margin-left: 1.5rem;
  list-style-type: disc;
}

.markdown-content p {
  margin-bottom: 1rem;
}

.markdown-content strong {
  font-weight: 600;
}

.markdown-content em {
  font-style: italic;
}
