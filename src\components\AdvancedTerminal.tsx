import React, { useState, useRef, useImperativeHandle, forwardRef, useEffect } from 'react';
import { Terminal as XTerm } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import { WebLinksAddon } from '@xterm/addon-web-links';
import { SearchAddon } from '@xterm/addon-search';
import { Unicode11Addon } from '@xterm/addon-unicode11';
import { WebglAddon } from '@xterm/addon-webgl';
import { SerializeAddon } from '@xterm/addon-serialize';
import { ImageAddon } from '@xterm/addon-image';
import { io, Socket } from 'socket.io-client';
import '@xterm/xterm/css/xterm.css';

export interface AdvancedTerminalRef {
  executeCommand: (command: string) => void;
  focus: () => void;
  clear: () => void;
  getTerminal: () => XTerm | null;
  resize: () => void;
  serialize: () => string;
  downloadLog: (filename?: string) => void;
  reconnect: () => void;
}

interface AdvancedTerminalProps {
  wsEndpoint?: string;
  defaultPath?: string;
  theme?: any;
  fontSize?: number;
  fontFamily?: string;
  showProcessOutput?: boolean;
  allowImages?: boolean;
  useWebGL?: boolean;
  useUnicode11?: boolean;
  initialCommand?: string;
  autoReconnect?: boolean;
  reconnectInterval?: number;
  showStatus?: boolean;
}

const AdvancedTerminal = forwardRef<AdvancedTerminalRef, AdvancedTerminalProps>((props, ref) => {
  const {
    wsEndpoint = 'http://localhost:3002',
    defaultPath = '/',
    theme = {
      background: '#1a1b26',
      foreground: '#c0caf5',
      cursor: '#c0caf5',
      black: '#15161E',
      red: '#f7768e',
      green: '#9ece6a',
      yellow: '#e0af68',
      blue: '#7aa2f7',
      magenta: '#bb9af7',
      cyan: '#7dcfff',
      white: '#a9b1d6',
      brightBlack: '#414868',
      brightRed: '#f7768e',
      brightGreen: '#9ece6a',
      brightYellow: '#e0af68',
      brightBlue: '#7aa2f7',
      brightMagenta: '#bb9af7',
      brightCyan: '#7dcfff',
      brightWhite: '#c0caf5'
    },
    fontSize = 14,
    fontFamily = 'Menlo, Monaco, "Courier New", monospace',
    showProcessOutput = true,
    allowImages = true,
    useWebGL = true,
    useUnicode11 = true,
    initialCommand,
    autoReconnect = true,
    reconnectInterval = 3000,
    showStatus = true
  } = props;

  const terminalRef = useRef<HTMLDivElement>(null);
  const xtermRef = useRef<XTerm | null>(null);
  const fitAddonRef = useRef<FitAddon | null>(null);
  const searchAddonRef = useRef<SearchAddon | null>(null);
  const webglAddonRef = useRef<WebglAddon | null>(null);
  const serializeAddonRef = useRef<SerializeAddon | null>(null);
  const imageAddonRef = useRef<ImageAddon | null>(null);
  const unicode11AddonRef = useRef<Unicode11Addon | null>(null);
  const socketRef = useRef<Socket | null>(null);
  const reconnectTimerRef = useRef<NodeJS.Timeout | null>(null);

  const [isConnected, setIsConnected] = useState(false);
  const [currentInput, setCurrentInput] = useState('');
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [terminalPid, setTerminalPid] = useState<number | null>(null);
  const [statusMessage, setStatusMessage] = useState<string>('Initializing...');
  const [isReconnecting, setIsReconnecting] = useState(false);

  // Initialize terminal
  useEffect(() => {
    if (!terminalRef.current) return;

    // Setup XTerm.js
    const term = new XTerm({
      cursorBlink: true,
      fontFamily,
      fontSize,
      theme,
      allowTransparency: true,
      scrollback: 10000,
      convertEol: true,
      allowProposedApi: true, // Required for some addons
      smoothScrollDuration: 300,
      rightClickSelectsWord: true,
      macOptionIsMeta: true,
      macOptionClickForcesSelection: true
    });

    // Add addons
    const fitAddon = new FitAddon();
    const searchAddon = new SearchAddon();
    const webLinksAddon = new WebLinksAddon();
    const serializeAddon = new SerializeAddon();

    term.loadAddon(fitAddon);
    term.loadAddon(searchAddon);
    term.loadAddon(webLinksAddon);
    term.loadAddon(serializeAddon);

    // Add Unicode11 addon if enabled
    if (useUnicode11) {
      try {
        const unicode11Addon = new Unicode11Addon();
        term.loadAddon(unicode11Addon);
        unicode11AddonRef.current = unicode11Addon;
      } catch (e) {
        console.warn('Unicode11 addon could not be loaded', e);
      }
    }

    // Add Image addon if enabled
    if (allowImages) {
      try {
        const imageAddon = new ImageAddon();
        term.loadAddon(imageAddon);
        imageAddonRef.current = imageAddon;
      } catch (e) {
        console.warn('Image addon could not be loaded', e);
      }
    }

    term.open(terminalRef.current);
    fitAddon.fit();

    // Try to load WebGL addon for better performance if enabled
    if (useWebGL) {
      try {
        const webglAddon = new WebglAddon();
        term.loadAddon(webglAddon);
        webglAddonRef.current = webglAddon;
      } catch (e) {
        console.warn('WebGL addon could not be loaded', e);
      }
    }

    xtermRef.current = term;
    fitAddonRef.current = fitAddon;
    searchAddonRef.current = searchAddon;
    serializeAddonRef.current = serializeAddon;

    // Connect to Socket.IO server
    connectToSocketIO();

    // Handle window resize
    const handleResize = () => {
      if (fitAddonRef.current && xtermRef.current) {
        fitAddonRef.current.fit();

        // Send terminal size to server
        if (socketRef.current && socketRef.current.connected && sessionId) {
          const { rows, cols } = term;
          socketRef.current.emit('resize', {
            cols,
            rows
          });
        }
      }
    };

    window.addEventListener('resize', handleResize);

    // Handle keyboard input
    term.onData((data) => {
      // Send data to server
      if (socketRef.current && socketRef.current.connected && sessionId) {
        socketRef.current.emit('input', {
          content: data
        });
      }
    });

    // Initial welcome message
    term.writeln('\x1b[1;34m# Advanced Terminal v2.0\x1b[0m');
    term.writeln('\x1b[1;34m# Connecting to server...\x1b[0m');
    term.writeln('');

    // Execute initial command if provided
    if (initialCommand && socketRef.current && socketRef.current.connected && sessionId) {
      setTimeout(() => {
        executeCommand(initialCommand);
      }, 1000);
    }

    return () => {
      window.removeEventListener('resize', handleResize);

      // Clear reconnect timer if active
      if (reconnectTimerRef.current) {
        clearTimeout(reconnectTimerRef.current);
      }

      // Clean up Socket.IO connection
      if (socketRef.current) {
        socketRef.current.disconnect();
      }

      // Clean up addons
      if (webglAddonRef.current) {
        webglAddonRef.current.dispose();
      }

      if (imageAddonRef.current) {
        imageAddonRef.current.dispose();
      }

      term.dispose();
    };
  }, []);

  // Connect to Socket.IO server
  const connectToSocketIO = () => {
    if (!xtermRef.current) return;

    const term = xtermRef.current;

    // Clear any existing connection
    if (socketRef.current) {
      socketRef.current.disconnect();
    }

    // Clear any existing reconnect timer
    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current);
    }

    setStatusMessage('Connecting to terminal server...');
    setIsReconnecting(false);

    // Create new Socket.IO connection
    const socket = io(wsEndpoint, {
      transports: ['websocket'],
      reconnection: false // We'll handle reconnection manually
    });

    // Connection established
    socket.on('connect', () => {
      setIsConnected(true);
      setStatusMessage('Connected to terminal server');

      if (showStatus) {
        term.writeln('\x1b[1;32m# Connected to terminal server\x1b[0m');
        term.writeln('');
      }
    });

    // Connection error
    socket.on('connect_error', (error) => {
      console.error('Socket.IO connection error:', error);
      setIsConnected(false);
      setStatusMessage(`Connection error: ${error.message}`);

      if (showStatus) {
        term.writeln(`\x1b[1;31m# Connection error: ${error.message}\x1b[0m`);
      }

      // Attempt to reconnect if autoReconnect is enabled
      if (autoReconnect && !isReconnecting) {
        handleReconnect();
      }
    });

    // Initial connection acknowledgment
    socket.on('connected', (data) => {
      console.log('Terminal server info:', data);

      // Create terminal session
      socket.emit('create', {
        cols: term.cols,
        rows: term.rows,
        cwd: defaultPath
      });
    });

    // Terminal session created
    socket.on('created', (data) => {
      setSessionId(data.sessionId);
      setTerminalPid(data.pid);
      setStatusMessage(`Terminal session created (PID: ${data.pid})`);

      if (showStatus) {
        term.writeln(`\x1b[1;32m# Terminal session created (PID: ${data.pid})\x1b[0m`);
        term.writeln('');
      }

      // Send initial path if provided
      if (defaultPath) {
        socket.emit('input', {
          content: `cd ${defaultPath}\r`
        });
      }
    });

    // Terminal output
    socket.on('output', (data) => {
      if (data.type === 'output' && data.content) {
        term.write(data.content);
      }
    });

    // Terminal error
    socket.on('error', (data) => {
      console.error('Terminal error:', data);
      setStatusMessage(`Error: ${data.message}`);

      if (showStatus) {
        term.writeln(`\x1b[1;31m# Error: ${data.message}\x1b[0m`);
      }
    });

    // Terminal resize acknowledgment
    socket.on('resized', (data) => {
      console.log(`Terminal resized to ${data.cols}x${data.rows}`);
    });

    // Terminal process exit
    socket.on('exit', (data) => {
      console.log(`Terminal process exited with code ${data.exitCode}`);
      setStatusMessage(`Process exited with code ${data.exitCode}`);

      if (showStatus) {
        term.writeln(`\x1b[1;33m# Process exited with code ${data.exitCode}\x1b[0m`);
      }

      // Clear session info
      setSessionId(null);
      setTerminalPid(null);

      // Create a new terminal session
      socket.emit('create', {
        cols: term.cols,
        rows: term.rows,
        cwd: defaultPath
      });
    });

    // Disconnection
    socket.on('disconnect', () => {
      setIsConnected(false);
      setStatusMessage('Disconnected from terminal server');

      if (showStatus) {
        term.writeln('\x1b[1;31m# Disconnected from terminal server\x1b[0m');
      }

      // Clear session info
      setSessionId(null);
      setTerminalPid(null);

      // Attempt to reconnect if autoReconnect is enabled
      if (autoReconnect && !isReconnecting) {
        handleReconnect();
      }
    });

    socketRef.current = socket;
  };

  // Handle reconnection
  const handleReconnect = () => {
    if (!xtermRef.current || isReconnecting) return;

    const term = xtermRef.current;
    setIsReconnecting(true);

    if (showStatus) {
      term.writeln(`\x1b[1;34m# Attempting to reconnect in ${reconnectInterval / 1000} seconds...\x1b[0m`);
    }

    setStatusMessage(`Reconnecting in ${reconnectInterval / 1000} seconds...`);

    // Set reconnect timer
    reconnectTimerRef.current = setTimeout(() => {
      if (showStatus) {
        term.writeln('\x1b[1;34m# Reconnecting...\x1b[0m');
      }
      connectToSocketIO();
    }, reconnectInterval);
  };

  // Execute command programmatically
  const executeCommand = (command: string) => {
    if (!xtermRef.current || !socketRef.current || !socketRef.current.connected || !sessionId) {
      console.error('Terminal or Socket.IO not ready');
      return;
    }

    const term = xtermRef.current;

    // Display command in terminal
    if (showProcessOutput) {
      term.writeln(`\r\n\x1b[1;36m$ ${command}\x1b[0m`);
    }

    // Send command to server
    socketRef.current.emit('input', {
      content: command + '\r'
    });
  };

  // Clear terminal
  const clearTerminal = () => {
    if (!xtermRef.current) return;
    xtermRef.current.clear();
  };

  // Resize terminal
  const resizeTerminal = () => {
    if (!xtermRef.current || !fitAddonRef.current || !socketRef.current || !socketRef.current.connected || !sessionId) {
      return;
    }

    fitAddonRef.current.fit();

    // Send terminal size to server
    const { rows, cols } = xtermRef.current;
    socketRef.current.emit('resize', {
      cols,
      rows
    });
  };

  // Serialize terminal content
  const serializeTerminal = (): string => {
    if (!xtermRef.current || !serializeAddonRef.current) {
      return '';
    }

    return serializeAddonRef.current.serialize();
  };

  // Download terminal log
  const downloadTerminalLog = (filename: string = 'terminal-log.txt') => {
    if (!xtermRef.current || !serializeAddonRef.current) {
      return;
    }

    const content = serializeAddonRef.current.serialize();
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();

    URL.revokeObjectURL(url);
  };

  // Reconnect to server
  const reconnectToServer = () => {
    if (isReconnecting) return;

    if (xtermRef.current) {
      xtermRef.current.writeln('\x1b[1;34m# Manually reconnecting to server...\x1b[0m');
    }

    connectToSocketIO();
  };

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    executeCommand,
    focus: () => {
      if (xtermRef.current) {
        xtermRef.current.focus();
      }
    },
    clear: clearTerminal,
    getTerminal: () => xtermRef.current,
    resize: resizeTerminal,
    serialize: serializeTerminal,
    downloadLog: downloadTerminalLog,
    reconnect: reconnectToServer
  }));

  return (
    <div className="h-full w-full overflow-hidden rounded-md border border-border bg-black relative">
      {/* Status indicator */}
      {showStatus && (
        <div className={`absolute top-2 right-2 z-10 flex items-center px-2 py-1 rounded text-xs ${
          isConnected ? 'bg-green-500/20 text-green-400' :
          isReconnecting ? 'bg-yellow-500/20 text-yellow-400' :
          'bg-red-500/20 text-red-400'
        }`}>
          <div className={`h-2 w-2 rounded-full mr-1.5 ${
            isConnected ? 'bg-green-500' :
            isReconnecting ? 'bg-yellow-500 animate-pulse' :
            'bg-red-500'
          }`} />
          <span>{statusMessage}</span>
        </div>
      )}

      {/* Terminal container */}
      <div ref={terminalRef} className="h-full w-full" />
    </div>
  );
});

AdvancedTerminal.displayName = 'AdvancedTerminal';

export default AdvancedTerminal;
