const express = require('express');
const http = require('http');
const { Server } = require('socket.io');
const cors = require('cors');
const os = require('os');
const path = require('path');
const fs = require('fs');

// Try to load node-pty, but make it optional
let pty;
try {
  pty = require('node-pty');
  console.log('node-pty loaded successfully');
} catch (error) {
  console.warn('node-pty could not be loaded:', error.message);
  console.warn('Terminal will run in simulation mode');
  pty = null;
}

// Create Express app
const app = express();
app.use(cors());
app.use(express.json());

// Create HTTP server
const server = http.createServer(app);

// Create Socket.IO server
const io = new Server(server, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST']
  }
});

// Determine shell based on platform
const shell = process.platform === 'win32' ? 'powershell.exe' : 'bash';
const defaultCwd = process.platform === 'win32' ? process.env.USERPROFILE : process.env.HOME;

// Store active terminal sessions
const sessions = new Map();

// Generate a unique session ID
const generateSessionId = () => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

// Create a new terminal process
const createTerminalProcess = (sessionId, options = {}) => {
  const {
    cwd = defaultCwd,
    cols = 80,
    rows = 24,
    env = process.env
  } = options;

  console.log(`Creating new terminal process for session ${sessionId}`);
  console.log(`CWD: ${cwd}, Cols: ${cols}, Rows: ${rows}`);

  // Ensure the working directory exists
  let workingDir = cwd;
  try {
    if (!fs.existsSync(workingDir)) {
      console.warn(`Directory ${workingDir} does not exist, falling back to default`);
      workingDir = defaultCwd;
    }
  } catch (error) {
    console.error(`Error checking directory: ${error.message}`);
    workingDir = defaultCwd;
  }

  // If node-pty is not available, create a simulated terminal process
  if (!pty) {
    console.log('Creating simulated terminal process');

    // Create a simulated terminal process
    const simulatedProcess = {
      pid: Math.floor(Math.random() * 10000),
      onData: (callback) => {
        simulatedProcess._dataCallback = callback;
        // Send initial welcome message
        callback(`Terminal running in simulation mode (node-pty not available)\r\n`);
        callback(`Current directory: ${workingDir}\r\n`);
        callback(`Type 'help' for available commands\r\n\r\n`);
        callback(`$ `);
      },
      onExit: (callback) => {
        simulatedProcess._exitCallback = callback;
      },
      write: (data) => {
        // Handle simulated commands
        if (data.includes('\r') || data.includes('\n')) {
          const command = simulatedProcess._currentCommand || '';
          simulatedProcess._currentCommand = '';

          if (command === 'help') {
            simulatedProcess._dataCallback(`\r\nAvailable commands:\r\n`);
            simulatedProcess._dataCallback(`  help     - Show this help\r\n`);
            simulatedProcess._dataCallback(`  ls       - List files\r\n`);
            simulatedProcess._dataCallback(`  pwd      - Show current directory\r\n`);
            simulatedProcess._dataCallback(`  echo     - Echo text\r\n`);
            simulatedProcess._dataCallback(`  clear    - Clear screen\r\n`);
            simulatedProcess._dataCallback(`  exit     - Exit terminal\r\n\r\n`);
          } else if (command === 'ls') {
            simulatedProcess._dataCallback(`\r\nSimulated file listing for ${workingDir}:\r\n`);
            simulatedProcess._dataCallback(`  file1.txt\r\n`);
            simulatedProcess._dataCallback(`  file2.js\r\n`);
            simulatedProcess._dataCallback(`  directory1/\r\n\r\n`);
          } else if (command === 'pwd') {
            simulatedProcess._dataCallback(`\r\n${workingDir}\r\n\r\n`);
          } else if (command.startsWith('echo ')) {
            const text = command.substring(5);
            simulatedProcess._dataCallback(`\r\n${text}\r\n\r\n`);
          } else if (command === 'clear') {
            simulatedProcess._dataCallback('\x1b[2J\x1b[H');
          } else if (command === 'exit') {
            simulatedProcess._dataCallback('\r\nExiting terminal...\r\n');
            if (simulatedProcess._exitCallback) {
              simulatedProcess._exitCallback({ exitCode: 0, signal: null });
            }
            return;
          } else if (command !== '') {
            simulatedProcess._dataCallback(`\r\nCommand not found: ${command}\r\n\r\n`);
          }

          simulatedProcess._dataCallback(`$ `);
        } else {
          // Echo the character
          simulatedProcess._dataCallback(data);

          // Store the current command
          if (data === '\b') {
            // Handle backspace
            if (simulatedProcess._currentCommand && simulatedProcess._currentCommand.length > 0) {
              simulatedProcess._currentCommand = simulatedProcess._currentCommand.substring(0, simulatedProcess._currentCommand.length - 1);
            }
          } else {
            simulatedProcess._currentCommand = (simulatedProcess._currentCommand || '') + data;
          }
        }
      },
      resize: (cols, rows) => {
        console.log(`Simulated terminal resized to ${cols}x${rows}`);
      },
      kill: () => {
        console.log(`Simulated terminal killed`);
        if (simulatedProcess._exitCallback) {
          simulatedProcess._exitCallback({ exitCode: 0, signal: null });
        }
      },
      _currentCommand: ''
    };

    return simulatedProcess;
  }

  // Create environment variables
  const processEnv = { ...env };

  // Create real terminal process with node-pty
  try {
    const ptyProcess = pty.spawn(shell, [], {
      name: 'xterm-256color',
      cols: cols,
      rows: rows,
      cwd: workingDir,
      env: processEnv
    });

    return ptyProcess;
  } catch (error) {
    console.error(`Error creating terminal process:`, error);

    // Fall back to simulated process if real process creation fails
    return createTerminalProcess(sessionId, options);
  }
};

// Socket.IO connection handler
io.on('connection', (socket) => {
  const sessionId = generateSessionId();
  console.log(`New connection established: ${sessionId}`);

  // Send initial connection acknowledgment
  socket.emit('connected', {
    sessionId,
    shell,
    platform: process.platform,
    defaultCwd
  });

  // Handle terminal creation
  socket.on('create', (options = {}) => {
    try {
      // Create a new terminal process for this session
      const ptyProcess = createTerminalProcess(sessionId, options);

      // Store the session
      sessions.set(sessionId, {
        socket,
        ptyProcess,
        options
      });

      // Send terminal output to client
      ptyProcess.onData((data) => {
        socket.emit('output', {
          type: 'output',
          content: data
        });
      });

      // Send terminal exit event to client
      ptyProcess.onExit(({ exitCode, signal }) => {
        socket.emit('exit', {
          type: 'exit',
          exitCode,
          signal
        });

        // Remove session on terminal exit
        if (sessions.has(sessionId)) {
          sessions.delete(sessionId);
        }
      });

      // Send acknowledgment
      socket.emit('created', {
        sessionId,
        pid: ptyProcess.pid
      });
    } catch (error) {
      console.error(`Error creating terminal: ${error.message}`);
      socket.emit('error', {
        type: 'error',
        message: `Failed to create terminal: ${error.message}`
      });
    }
  });

  // Handle terminal input
  socket.on('input', (data) => {
    if (!sessions.has(sessionId)) {
      socket.emit('error', {
        type: 'error',
        message: 'Terminal session not found'
      });
      return;
    }

    try {
      const { ptyProcess } = sessions.get(sessionId);
      ptyProcess.write(data.content);
    } catch (error) {
      console.error(`Error writing to terminal: ${error.message}`);
      socket.emit('error', {
        type: 'error',
        message: `Failed to write to terminal: ${error.message}`
      });
    }
  });

  // Handle terminal resize
  socket.on('resize', (data) => {
    if (!sessions.has(sessionId)) {
      socket.emit('error', {
        type: 'error',
        message: 'Terminal session not found'
      });
      return;
    }

    try {
      const { ptyProcess } = sessions.get(sessionId);
      ptyProcess.resize(data.cols, data.rows);

      // Update stored options
      const session = sessions.get(sessionId);
      session.options.cols = data.cols;
      session.options.rows = data.rows;

      socket.emit('resized', {
        cols: data.cols,
        rows: data.rows
      });
    } catch (error) {
      console.error(`Error resizing terminal: ${error.message}`);
      socket.emit('error', {
        type: 'error',
        message: `Failed to resize terminal: ${error.message}`
      });
    }
  });

  // Handle process kill request
  socket.on('kill', (data) => {
    if (!sessions.has(sessionId)) {
      socket.emit('error', {
        type: 'error',
        message: 'Terminal session not found'
      });
      return;
    }

    try {
      const { ptyProcess } = sessions.get(sessionId);

      if (data.pid && data.pid !== ptyProcess.pid) {
        // Kill specific process
        process.kill(data.pid);
        socket.emit('output', {
          type: 'output',
          content: `\r\nProcess ${data.pid} killed\r\n`
        });
      } else {
        // Kill the terminal process
        ptyProcess.kill();
        sessions.delete(sessionId);
        socket.emit('killed', { sessionId });
      }
    } catch (error) {
      console.error(`Error killing process: ${error.message}`);
      socket.emit('error', {
        type: 'error',
        message: `Failed to kill process: ${error.message}`
      });
    }
  });

  // Handle client disconnection
  socket.on('disconnect', () => {
    console.log(`Connection closed: ${sessionId}`);

    // Kill the terminal process
    if (sessions.has(sessionId)) {
      const { ptyProcess } = sessions.get(sessionId);
      ptyProcess.kill();
      sessions.delete(sessionId);
    }
  });
});

// API routes
app.get('/api/status', (req, res) => {
  res.json({
    status: 'running',
    sessions: sessions.size,
    platform: process.platform,
    shell,
    defaultCwd
  });
});

app.get('/api/sessions', (req, res) => {
  const sessionInfo = Array.from(sessions.entries()).map(([id, { options, ptyProcess }]) => ({
    id,
    pid: ptyProcess.pid,
    cwd: options.cwd || defaultCwd,
    cols: options.cols || 80,
    rows: options.rows || 24
  }));

  res.json(sessionInfo);
});

// Handle server shutdown
process.on('SIGINT', () => {
  console.log('Shutting down terminal server...');

  // Kill all terminal processes
  for (const [sessionId, { ptyProcess }] of sessions.entries()) {
    console.log(`Killing terminal process for session ${sessionId}`);
    ptyProcess.kill();
  }

  // Close the server
  server.close(() => {
    console.log('Terminal server shut down');
    process.exit(0);
  });
});

// Start server
const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`Terminal server running on http://localhost:${PORT}`);
  console.log(`Socket.IO endpoint: ws://localhost:${PORT}`);
  console.log(`Using shell: ${shell}`);
  console.log(`Default working directory: ${defaultCwd}`);
});
