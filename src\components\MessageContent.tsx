import React from 'react';
import { cn } from '@/lib/utils';

interface MessageContentProps {
  content: string;
  role: 'user' | 'assistant';
}

const MessageContent: React.FC<MessageContentProps> = ({ content, role }) => {
  // Clean and format the content
  const formatContent = (text: string): string => {
    return text
      // Remove HTML tags but keep their content
      .replace(/<strong>(.*?)<\/strong>/g, '$1')
      .replace(/<code[^>]*>(.*?)<\/code>/g, '$1')
      .replace(/<pre[^>]*>(.*?)<\/pre>/g, '$1')
      .replace(/<[^>]+>/g, '') // Remove any remaining HTML tags
      
      // Clean markdown formatting for better readability
      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold markdown
      .replace(/\*(.*?)\*/g, '$1')     // Remove italic markdown
      .replace(/#{1,6}\s/g, '')        // Remove markdown headers
      .replace(/^\s*[-*+]\s/gm, '• ')  // Convert markdown lists to bullet points
      .replace(/\n{3,}/g, '\n\n')      // Remove excessive line breaks
      .trim();
  };

  // Split content into paragraphs and code blocks
  const parseContent = (text: string) => {
    const parts = [];
    let currentText = text;
    let partIndex = 0;

    // Extract code blocks first
    const codeBlockRegex = /```[\s\S]*?```/g;
    let match;
    let lastIndex = 0;

    while ((match = codeBlockRegex.exec(currentText)) !== null) {
      // Add text before code block
      if (match.index > lastIndex) {
        const beforeText = currentText.slice(lastIndex, match.index).trim();
        if (beforeText) {
          parts.push({
            type: 'text',
            content: formatContent(beforeText),
            key: `text-${partIndex++}`
          });
        }
      }

      // Add code block
      const codeContent = match[0]
        .replace(/```(\w+)?\n?/, '')
        .replace(/```$/, '')
        .trim();
      
      if (codeContent) {
        parts.push({
          type: 'code',
          content: codeContent,
          key: `code-${partIndex++}`
        });
      }

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < currentText.length) {
      const remainingText = currentText.slice(lastIndex).trim();
      if (remainingText) {
        parts.push({
          type: 'text',
          content: formatContent(remainingText),
          key: `text-${partIndex++}`
        });
      }
    }

    // If no code blocks found, treat entire content as text
    if (parts.length === 0) {
      parts.push({
        type: 'text',
        content: formatContent(currentText),
        key: 'text-0'
      });
    }

    return parts;
  };

  const contentParts = parseContent(content);

  return (
    <div className="space-y-2">
      {contentParts.map((part) => {
        if (part.type === 'code') {
          return (
            <div
              key={part.key}
              className="bg-gray-900 text-gray-100 p-3 rounded-md font-mono text-sm overflow-x-auto border"
            >
              <pre className="whitespace-pre-wrap">{part.content}</pre>
            </div>
          );
        }

        return (
          <div
            key={part.key}
            className={cn(
              "text-sm whitespace-pre-wrap leading-relaxed",
              role === 'assistant' ? "text-blue-900" : "text-gray-900"
            )}
          >
            {part.content.split('\n').map((line, index) => (
              <div key={index} className={line.trim() === '' ? 'h-2' : ''}>
                {line}
              </div>
            ))}
          </div>
        );
      })}
    </div>
  );
};

export default MessageContent;
