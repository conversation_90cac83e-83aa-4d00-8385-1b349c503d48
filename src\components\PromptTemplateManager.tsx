import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Plus, Trash2, Edit, Copy, Star, StarOff, Sparkles } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  template: string;
  variables: string[];
  isFavorite: boolean;
  createdAt: string;
  updatedAt: string;
}

const PromptTemplateManager: React.FC<{
  onSelectTemplate: (template: string) => void;
}> = ({ onSelectTemplate }) => {
  const { toast } = useToast();
  const [templates, setTemplates] = useState<PromptTemplate[]>([]);
  const [isAddingTemplate, setIsAddingTemplate] = useState(false);
  const [isEditingTemplate, setIsEditingTemplate] = useState(false);
  const [editingTemplateId, setEditingTemplateId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [newTemplate, setNewTemplate] = useState<Omit<PromptTemplate, 'id' | 'createdAt' | 'updatedAt' | 'variables'>>({
    name: '',
    description: '',
    category: 'general',
    template: '',
    isFavorite: false
  });
  
  // Load templates on component mount
  useEffect(() => {
    loadTemplates();
  }, []);
  
  // Load templates from localStorage
  const loadTemplates = () => {
    try {
      const savedTemplates = localStorage.getItem('prompt-templates');
      if (savedTemplates) {
        setTemplates(JSON.parse(savedTemplates));
      } else {
        // Initialize with default templates
        const defaultTemplates: PromptTemplate[] = [
          {
            id: 'template-1',
            name: 'Create React Component',
            description: 'Generate a React functional component with TypeScript',
            category: 'react',
            template: `Create a React functional component named {{componentName}} that {{functionality}}.

Requirements:
- Use TypeScript with proper type definitions
- Include proper props interface
- Use React hooks where appropriate
- Add JSDoc comments
- Follow best practices for React components

The component should be exported as the default export.`,
            variables: ['componentName', 'functionality'],
            isFavorite: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: 'template-2',
            name: 'Fix Bug',
            description: 'Analyze and fix a bug in the code',
            category: 'debugging',
            template: `I have a bug in my code where {{bugDescription}}.

Here's the relevant code:

\`\`\`{{language}}
{{code}}
\`\`\`

Expected behavior: {{expectedBehavior}}
Actual behavior: {{actualBehavior}}

Please help me identify the issue and provide a fixed version of the code with an explanation of what was wrong.`,
            variables: ['bugDescription', 'language', 'code', 'expectedBehavior', 'actualBehavior'],
            isFavorite: false,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: 'template-3',
            name: 'Create API Endpoint',
            description: 'Generate code for a REST API endpoint',
            category: 'backend',
            template: `Create a {{framework}} API endpoint that {{functionality}}.

Requirements:
- HTTP Method: {{method}}
- Route: {{route}}
- Request parameters/body: {{requestParams}}
- Response format: {{responseFormat}}
- Error handling for common cases
- Input validation

Please include any necessary imports and middleware.`,
            variables: ['framework', 'functionality', 'method', 'route', 'requestParams', 'responseFormat'],
            isFavorite: false,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ];
        
        setTemplates(defaultTemplates);
        localStorage.setItem('prompt-templates', JSON.stringify(defaultTemplates));
      }
    } catch (error) {
      console.error('Error loading prompt templates:', error);
      toast({
        title: 'Error',
        description: 'Failed to load prompt templates',
        variant: 'destructive',
      });
    }
  };
  
  // Save templates to localStorage
  const saveTemplates = (updatedTemplates: PromptTemplate[]) => {
    try {
      localStorage.setItem('prompt-templates', JSON.stringify(updatedTemplates));
    } catch (error) {
      console.error('Error saving prompt templates:', error);
      toast({
        title: 'Error',
        description: 'Failed to save prompt templates',
        variant: 'destructive',
      });
    }
  };
  
  // Add new template
  const handleAddTemplate = () => {
    try {
      if (!newTemplate.name || !newTemplate.template) {
        toast({
          title: 'Validation Error',
          description: 'Name and template content are required',
          variant: 'destructive',
        });
        return;
      }
      
      // Extract variables from template
      const variableRegex = /{{([a-zA-Z0-9_]+)}}/g;
      const variables: string[] = [];
      let match;
      
      while ((match = variableRegex.exec(newTemplate.template)) !== null) {
        if (!variables.includes(match[1])) {
          variables.push(match[1]);
        }
      }
      
      const now = new Date().toISOString();
      const newTemplateWithId: PromptTemplate = {
        ...newTemplate,
        id: `template-${Date.now()}`,
        variables,
        createdAt: now,
        updatedAt: now
      };
      
      const updatedTemplates = [...templates, newTemplateWithId];
      setTemplates(updatedTemplates);
      saveTemplates(updatedTemplates);
      
      setIsAddingTemplate(false);
      resetNewTemplate();
      
      toast({
        title: 'Template Added',
        description: `${newTemplate.name} has been added successfully`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to add template: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    }
  };
  
  // Update existing template
  const handleUpdateTemplate = () => {
    try {
      if (!editingTemplateId) return;
      
      if (!newTemplate.name || !newTemplate.template) {
        toast({
          title: 'Validation Error',
          description: 'Name and template content are required',
          variant: 'destructive',
        });
        return;
      }
      
      // Extract variables from template
      const variableRegex = /{{([a-zA-Z0-9_]+)}}/g;
      const variables: string[] = [];
      let match;
      
      while ((match = variableRegex.exec(newTemplate.template)) !== null) {
        if (!variables.includes(match[1])) {
          variables.push(match[1]);
        }
      }
      
      const updatedTemplates = templates.map(template => {
        if (template.id === editingTemplateId) {
          return {
            ...template,
            ...newTemplate,
            variables,
            updatedAt: new Date().toISOString()
          };
        }
        return template;
      });
      
      setTemplates(updatedTemplates);
      saveTemplates(updatedTemplates);
      
      setIsEditingTemplate(false);
      setEditingTemplateId(null);
      resetNewTemplate();
      
      toast({
        title: 'Template Updated',
        description: `${newTemplate.name} has been updated successfully`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to update template: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    }
  };
  
  // Delete template
  const handleDeleteTemplate = (templateId: string) => {
    try {
      const templateToDelete = templates.find(t => t.id === templateId);
      if (!templateToDelete) return;
      
      const updatedTemplates = templates.filter(template => template.id !== templateId);
      setTemplates(updatedTemplates);
      saveTemplates(updatedTemplates);
      
      toast({
        title: 'Template Deleted',
        description: `${templateToDelete.name} has been deleted successfully`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to delete template: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    }
  };
  
  // Toggle favorite status
  const handleToggleFavorite = (templateId: string) => {
    try {
      const updatedTemplates = templates.map(template => {
        if (template.id === templateId) {
          return {
            ...template,
            isFavorite: !template.isFavorite,
            updatedAt: new Date().toISOString()
          };
        }
        return template;
      });
      
      setTemplates(updatedTemplates);
      saveTemplates(updatedTemplates);
      
      const template = updatedTemplates.find(t => t.id === templateId);
      if (template) {
        toast({
          title: template.isFavorite ? 'Added to Favorites' : 'Removed from Favorites',
          description: `${template.name} has been ${template.isFavorite ? 'added to' : 'removed from'} favorites`,
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to update favorite status: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    }
  };
  
  // Edit template
  const handleEditTemplate = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setNewTemplate({
        name: template.name,
        description: template.description,
        category: template.category,
        template: template.template,
        isFavorite: template.isFavorite
      });
      setEditingTemplateId(templateId);
      setIsEditingTemplate(true);
    }
  };
  
  // Reset new template form
  const resetNewTemplate = () => {
    setNewTemplate({
      name: '',
      description: '',
      category: 'general',
      template: '',
      isFavorite: false
    });
  };
  
  // Use template
  const handleUseTemplate = (template: PromptTemplate) => {
    // If there are variables, show a dialog to fill them in
    if (template.variables.length > 0) {
      // For now, we'll just use the template as is
      // In a real implementation, you would show a dialog to fill in the variables
      onSelectTemplate(template.template);
    } else {
      onSelectTemplate(template.template);
    }
    
    toast({
      title: 'Template Applied',
      description: `${template.name} has been applied to the prompt`,
    });
  };
  
  // Copy template to clipboard
  const handleCopyTemplate = (template: PromptTemplate) => {
    navigator.clipboard.writeText(template.template);
    
    toast({
      title: 'Copied to Clipboard',
      description: `${template.name} has been copied to clipboard`,
    });
  };
  
  // Filter templates based on search query and category
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = 
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.template.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = 
      selectedCategory === 'all' || 
      selectedCategory === 'favorites' && template.isFavorite ||
      template.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });
  
  // Get unique categories
  const categories = ['all', 'favorites', ...new Set(templates.map(t => t.category))];
  
  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <span>Prompt Templates</span>
          <Button variant="outline" size="sm" onClick={() => setIsAddingTemplate(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Template
          </Button>
        </CardTitle>
        
        <div className="flex items-center space-x-2 mt-2">
          <Input
            placeholder="Search templates..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="flex-1"
          />
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map(category => (
                <SelectItem key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          {filteredTemplates.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>No templates found</p>
              <p className="text-sm">Try a different search or category</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredTemplates.map(template => (
                <div 
                  key={template.id}
                  className="p-4 rounded-lg border border-border hover:border-primary/50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <h3 className="font-medium text-lg">{template.name}</h3>
                      {template.isFavorite && (
                        <Badge variant="secondary" className="ml-2">
                          <Star className="h-3 w-3 mr-1 fill-current" />
                          Favorite
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleToggleFavorite(template.id)}
                      >
                        {template.isFavorite ? (
                          <StarOff className="h-4 w-4" />
                        ) : (
                          <Star className="h-4 w-4" />
                        )}
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleCopyTemplate(template)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleEditTemplate(template.id)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        className="text-destructive hover:text-destructive hover:bg-destructive/10"
                        onClick={() => handleDeleteTemplate(template.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="mt-2 text-sm text-muted-foreground">
                    {template.description}
                  </div>
                  
                  <div className="mt-2 flex items-center">
                    <Badge variant="outline" className="mr-2">
                      {template.category}
                    </Badge>
                    {template.variables.length > 0 && (
                      <Badge variant="outline" className="bg-primary/5">
                        {template.variables.length} variable{template.variables.length !== 1 ? 's' : ''}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="mt-4 bg-muted p-3 rounded-md text-sm max-h-32 overflow-auto">
                    <pre className="whitespace-pre-wrap">{template.template}</pre>
                  </div>
                  
                  <div className="mt-4 flex justify-end">
                    <Button 
                      variant="default" 
                      size="sm"
                      onClick={() => handleUseTemplate(template)}
                    >
                      <Sparkles className="h-4 w-4 mr-2" />
                      Use Template
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
      
      {/* Add Template Dialog */}
      <Dialog open={isAddingTemplate} onOpenChange={setIsAddingTemplate}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Prompt Template</DialogTitle>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="template-name">Template Name</Label>
                <Input 
                  id="template-name"
                  value={newTemplate.name}
                  onChange={(e) => setNewTemplate({...newTemplate, name: e.target.value})}
                  placeholder="e.g., Create React Component"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="template-category">Category</Label>
                <Select 
                  value={newTemplate.category}
                  onValueChange={(value) => setNewTemplate({...newTemplate, category: value})}
                >
                  <SelectTrigger id="template-category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">General</SelectItem>
                    <SelectItem value="react">React</SelectItem>
                    <SelectItem value="backend">Backend</SelectItem>
                    <SelectItem value="debugging">Debugging</SelectItem>
                    <SelectItem value="database">Database</SelectItem>
                    <SelectItem value="testing">Testing</SelectItem>
                    <SelectItem value="devops">DevOps</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="template-description">Description</Label>
              <Input 
                id="template-description"
                value={newTemplate.description}
                onChange={(e) => setNewTemplate({...newTemplate, description: e.target.value})}
                placeholder="Brief description of what this template does"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="template-content">Template Content</Label>
              <div className="text-xs text-muted-foreground mb-2">
                Use {{variableName}} syntax for variables that will be replaced when using the template.
              </div>
              <Textarea 
                id="template-content"
                value={newTemplate.template}
                onChange={(e) => setNewTemplate({...newTemplate, template: e.target.value})}
                placeholder="Enter your prompt template here..."
                className="min-h-[200px] font-mono"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsAddingTemplate(false);
              resetNewTemplate();
            }}>
              Cancel
            </Button>
            <Button onClick={handleAddTemplate}>Add Template</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Edit Template Dialog */}
      <Dialog open={isEditingTemplate} onOpenChange={setIsEditingTemplate}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Prompt Template</DialogTitle>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-template-name">Template Name</Label>
                <Input 
                  id="edit-template-name"
                  value={newTemplate.name}
                  onChange={(e) => setNewTemplate({...newTemplate, name: e.target.value})}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="edit-template-category">Category</Label>
                <Select 
                  value={newTemplate.category}
                  onValueChange={(value) => setNewTemplate({...newTemplate, category: value})}
                >
                  <SelectTrigger id="edit-template-category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">General</SelectItem>
                    <SelectItem value="react">React</SelectItem>
                    <SelectItem value="backend">Backend</SelectItem>
                    <SelectItem value="debugging">Debugging</SelectItem>
                    <SelectItem value="database">Database</SelectItem>
                    <SelectItem value="testing">Testing</SelectItem>
                    <SelectItem value="devops">DevOps</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-template-description">Description</Label>
              <Input 
                id="edit-template-description"
                value={newTemplate.description}
                onChange={(e) => setNewTemplate({...newTemplate, description: e.target.value})}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-template-content">Template Content</Label>
              <div className="text-xs text-muted-foreground mb-2">
                Use {{variableName}} syntax for variables that will be replaced when using the template.
              </div>
              <Textarea 
                id="edit-template-content"
                value={newTemplate.template}
                onChange={(e) => setNewTemplate({...newTemplate, template: e.target.value})}
                className="min-h-[200px] font-mono"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsEditingTemplate(false);
              setEditingTemplateId(null);
              resetNewTemplate();
            }}>
              Cancel
            </Button>
            <Button onClick={handleUpdateTemplate}>Update Template</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default PromptTemplateManager;