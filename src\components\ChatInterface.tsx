import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, Send, Sparkles, User, Trash2, MessageSquare } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import MessageContent from './MessageContent';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  isLoading?: boolean;
}

interface ChatInterfaceProps {
  onSendMessage: (message: string) => Promise<string>;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ onSendMessage }) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 'welcome-1',
      role: 'assistant',
      content: 'Merhaba! Ben AI Kod Asistanınız. Size projelerinizde yardımcı olmak için buradayım. Nasıl yardımcı olabilirim?',
      timestamp: new Date(),
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);



  // Scroll to the bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSend = async () => {
    if (!inputValue.trim() || isProcessing) return;

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: inputValue,
      timestamp: new Date(),
    };

    const loadingMessage: Message = {
      id: `assistant-${Date.now()}`,
      role: 'assistant',
      content: '',
      timestamp: new Date(),
      isLoading: true,
    };

    setMessages((prev) => [...prev, userMessage, loadingMessage]);
    const currentInput = inputValue;
    setInputValue('');
    setIsProcessing(true);

    try {
      const response = await onSendMessage(currentInput);

      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === loadingMessage.id
            ? { ...msg, content: response, isLoading: false }
            : msg
        )
      );
    } catch (error) {
      console.error('Error processing message:', error);

      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === loadingMessage.id
            ? {
                ...msg,
                content: 'Üzgünüm, bir hata oluştu. Lütfen tekrar deneyin.',
                isLoading: false
              }
            : msg
        )
      );
    } finally {
      setIsProcessing(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const clearMessages = () => {
    setMessages([
      {
        id: 'welcome-1',
        role: 'assistant',
        content: 'Merhaba! Ben AI Kod Asistanınız. Size projelerinizde yardımcı olmak için buradayım. Nasıl yardımcı olabilirim?',
        timestamp: new Date(),
      }
    ]);
  };

  const renderMessage = (message: Message) => {
    return (
      <div className="flex gap-3 my-4">
        <div className={cn(
          "w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium",
          message.role === 'assistant' ? "bg-blue-500" : "bg-gray-500"
        )}>
          {message.role === 'assistant' ?
            <Sparkles className="h-4 w-4" /> :
            <User className="h-4 w-4" />
          }
        </div>

        <div className="flex-1 space-y-2">
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm">
              {message.role === 'assistant' ? 'AI Asistan' : 'Siz'}
            </span>
            <span className="text-xs text-muted-foreground">
              {new Date(message.timestamp).toLocaleTimeString()}
            </span>
          </div>

          {message.isLoading ? (
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Yanıt oluşturuluyor...</span>
            </div>
          ) : (
            <div className={cn(
              "rounded-lg p-3",
              message.role === 'assistant'
                ? "bg-blue-50 border border-blue-200"
                : "bg-gray-50 border border-gray-200"
            )}>
              <MessageContent content={message.content} role={message.role} />
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b flex items-center justify-between">
        <div className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5 text-primary" />
          <h2 className="font-medium">AI Sohbet</h2>
        </div>
        <Button variant="ghost" size="sm" onClick={clearMessages}>
          <Trash2 className="h-4 w-4 mr-2" />
          Temizle
        </Button>
      </div>

      <ScrollArea className="flex-1 p-4">
        {messages.map((message) => (
          <div key={message.id}>
            {renderMessage(message)}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </ScrollArea>

      <div className="border-t p-4">
        <div className="flex gap-3">
          <Textarea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="AI Asistana bir soru sorun..."
            className="flex-1 min-h-[60px] max-h-[120px] resize-none"
            disabled={isProcessing}
          />
          <Button
            onClick={handleSend}
            disabled={!inputValue.trim() || isProcessing}
            className="self-end"
          >
            {isProcessing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>
        <div className="text-xs text-muted-foreground mt-2">
          <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Ctrl</kbd>+<kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Enter</kbd> ile gönder
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;