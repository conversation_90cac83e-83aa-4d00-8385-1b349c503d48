import React, { useState, useRef, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar } from '@/components/ui/avatar';
import { Loader2, Send, Sparkles, User, Trash2, MessageSquare, Settings } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { aiModelService } from '@/services/aiModelService';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  status?: 'loading' | 'success' | 'error';
  actions?: {
    title: string;
    description: string;
    status: 'pending' | 'complete' | 'error';
    output?: string;
  }[];
}

interface ChatInterfaceProps {
  onSendMessage: (message: string) => Promise<string>;
  initialSystemMessage?: string;
  onActionsCompleted?: (actions: any[]) => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  onSendMessage,
  initialSystemMessage = "Merhaba! Ben AI Kod Asistanınız. Size projelerinizde, kod yazımında ve geliştirme süreçlerinde yardımcı olmak için buradayım. Nasıl yardımcı olabilirim?",
  onActionsCompleted
}) => {
  const { toast } = useToast();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 'system-1',
      role: 'system',
      content: initialSystemMessage,
      timestamp: new Date(),
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const [promptHistory, setPromptHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  // Load prompt history from localStorage on component mount
  useEffect(() => {
    const savedHistory = localStorage.getItem('chat-history');
    if (savedHistory) {
      setPromptHistory(JSON.parse(savedHistory));
    }
    
    const savedMessages = localStorage.getItem('chat-messages');
    if (savedMessages) {
      try {
        setMessages(JSON.parse(savedMessages));
      } catch (error) {
        console.error('Failed to parse saved messages:', error);
      }
    }
  }, []);

  // Save messages to localStorage when they change
  useEffect(() => {
    localStorage.setItem('chat-messages', JSON.stringify(messages));
  }, [messages]);

  // Scroll to the bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSend = async () => {
    if (!inputValue.trim() || isProcessing) return;

    // Create a new user message
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: inputValue,
      timestamp: new Date(),
    };

    // Create a placeholder assistant message
    const assistantMessage: Message = {
      id: `assistant-${Date.now()}`,
      role: 'assistant',
      content: '',
      timestamp: new Date(),
      status: 'loading',
    };

    // Add messages to state
    setMessages((prev) => [...prev, userMessage, assistantMessage]);
    
    // Save to history
    const newHistory = [inputValue, ...promptHistory.slice(0, 19)]; // Keep last 20 prompts
    setPromptHistory(newHistory);
    localStorage.setItem('chat-history', JSON.stringify(newHistory));
    setHistoryIndex(-1);
    
    // Clear input
    setInputValue('');
    
    // Process the message
    setIsProcessing(true);
    
    try {
      // Get response from API
      const response = await onSendMessage(inputValue);
      
      // Update the assistant message with the response
      setMessages((prev) => 
        prev.map((msg) => 
          msg.id === assistantMessage.id 
            ? { ...msg, content: response, status: 'success' } 
            : msg
        )
      );
      
      toast({
        title: "Yanıt alındı",
        description: "AI yanıtınızı gönderdi.",
      });
    } catch (error) {
      console.error('Error processing message:', error);
      
      // Update the assistant message with an error
      setMessages((prev) => 
        prev.map((msg) => 
          msg.id === assistantMessage.id 
            ? { 
                ...msg, 
                content: 'Üzgünüm, mesajınızı işlerken bir hata oluştu. Lütfen tekrar deneyin.', 
                status: 'error' 
              } 
            : msg
        )
      );
      
      toast({
        title: "Hata",
        description: "Mesajınız işlenirken bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      e.preventDefault();
      handleSend();
    } else if (e.key === 'ArrowUp' && e.ctrlKey) {
      e.preventDefault();
      // Navigate up through history
      if (promptHistory.length > 0 && historyIndex < promptHistory.length - 1) {
        const newIndex = historyIndex + 1;
        setHistoryIndex(newIndex);
        setInputValue(promptHistory[newIndex]);
      }
    } else if (e.key === 'ArrowDown' && e.ctrlKey) {
      e.preventDefault();
      // Navigate down through history
      if (historyIndex > 0) {
        const newIndex = historyIndex - 1;
        setHistoryIndex(newIndex);
        setInputValue(promptHistory[newIndex]);
      } else if (historyIndex === 0) {
        setHistoryIndex(-1);
        setInputValue('');
      }
    }
  };

  const clearMessages = () => {
    const systemMessage = messages.find(msg => msg.role === 'system');
    const newMessages = systemMessage ? [systemMessage] : [];
    setMessages(newMessages);
    localStorage.setItem('chat-messages', JSON.stringify(newMessages));
    toast({
      title: "Sohbet temizlendi",
      description: "Tüm sohbet geçmişi temizlendi.",
    });
  };

  const clearPrompt = () => {
    setInputValue('');
    setHistoryIndex(-1);
  };

  // Get active AI model
  const activeModel = aiModelService.getActiveModel();

  const renderMessage = (message: Message) => {
    if (message.role === 'system') {
      return (
        <Card className="p-4 bg-primary/10 border-primary/20 my-2 rounded-lg">
          <div className="flex items-start gap-2">
            <Sparkles className="h-5 w-5 text-primary mt-0.5" />
            <div className="flex-1">
              <div className="text-sm prose prose-sm max-w-none">
                {message.content}
              </div>
            </div>
          </div>
        </Card>
      );
    }

    return (
      <div className={cn(
        "flex gap-4 my-4",
        message.role === 'assistant' ? "items-start" : "items-start"
      )}>
        <Avatar className={cn(
          "h-8 w-8 rounded-md",
          message.role === 'assistant' ? "bg-primary/20 text-primary" : "bg-secondary/20"
        )}>
          {message.role === 'assistant' ? 
            <Sparkles className="h-4 w-4" /> : 
            <User className="h-4 w-4" />
          }
        </Avatar>
        
        <div className="flex-1 space-y-2">
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm">
              {message.role === 'assistant' ? 'AI Asistan' : 'Siz'}
            </span>
            <span className="text-xs text-muted-foreground">
              {new Date(message.timestamp).toLocaleTimeString()}
            </span>
            {message.role === 'assistant' && activeModel && (
              <Badge variant="outline" className="text-xs">
                {activeModel.name}
              </Badge>
            )}
          </div>
          
          {message.status === 'loading' ? (
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Yanıt oluşturuluyor...</span>
            </div>
          ) : (
            <div className="prose prose-sm max-w-none break-words whitespace-pre-wrap">
              {message.content}
            </div>
          )}
          
          {message.actions && message.actions.length > 0 && (
            <div className="mt-2 space-y-2">
              <div className="text-sm font-medium">İşlemler:</div>
              <div className="space-y-1">
                {message.actions.map((action, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <div className={cn(
                      "h-2 w-2 rounded-full",
                      action.status === 'complete' ? "bg-green-500" : 
                      action.status === 'error' ? "bg-red-500" : "bg-yellow-500"
                    )} />
                    <span>{action.title}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b flex items-center justify-between">
        <div className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5 text-primary" />
          <h2 className="font-medium">AI Sohbet</h2>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <Settings className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={clearMessages}>
              <Trash2 className="h-4 w-4 mr-2" />
              Sohbeti Temizle
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      <div className="flex-1 p-4 overflow-y-auto">
        {messages.map((message) => (
          <div key={message.id}>
            {renderMessage(message)}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
      
      <div className="border-t border-border p-4 bg-background/80 backdrop-blur-sm">
        <div className="flex items-start gap-3">
          <div className="relative flex-1">
            <Textarea
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="AI Asistana bir soru sorun veya komut verin..."
              className={cn(
                "flex-1 min-h-[80px] max-h-[200px] resize-none bg-muted/30 pr-10",
                "focus:ring-1 focus:ring-offset-0 focus:ring-primary modern-input"
              )}
              disabled={isProcessing}
            />
            {inputValue.length > 0 ? (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-2 top-2 h-6 w-6 text-muted-foreground hover:text-foreground"
                onClick={clearPrompt}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            ) : (
              <Sparkles 
                className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" 
              />
            )}
          </div>
          <Button 
            onClick={handleSend}
            className="h-10 px-4 modern-button"
            disabled={!inputValue.trim() || isProcessing}
          >
            {isProcessing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
            <span className="ml-2">Gönder</span>
          </Button>
        </div>
        <div className="text-xs text-muted-foreground mt-2 flex items-center justify-between">
          <div>
            <span className="mr-4">
              <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Ctrl</kbd>+<kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Enter</kbd> gönder
            </span>
            <span>
              <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Ctrl</kbd>+<kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">↑/↓</kbd> geçmiş
            </span>
          </div>
          <div className="text-right">
            <span className="text-primary">AI-powered</span> sohbet
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;