# Gelişmiş Terminal Özellikleri v2.0

Bu projede üç farklı terminal seçeneği bulunmaktadır:

1. **Temel Terminal**: Basit komut simülasyonu sağlayan temel terminal
2. **Gelişmiş Terminal**: Socket.IO üzerinden gerçek terminal emülasyonu sağlayan gelişmiş terminal
3. **Terminal Yöneticisi**: Birden fazla terminal oturumunu yönetmenizi sağlayan terminal yöneticisi

## Kurulum

Gelişmiş terminal özelliklerini kullanmak için öncelikle terminal sunucusunu başlatmanız gerekmektedir:

### Windows

```
start-terminal-server.bat
```

### Linux/Mac

```
chmod +x start-terminal-server.sh
./start-terminal-server.sh
```

## Gereksinimler

Terminal sunucusu için aşağıdaki paketlerin yüklü olması gerekmektedir:

- Node.js (v14 veya üzeri)
- npm
- node-pty (Windows'ta derleyici gerektirebilir)
- express (HTTP sunucusu)
- socket.io (WebSocket kütüphanesi)
- cors (Cross-Origin Resource Sharing)

## Özellikler

### Gelişmiş Terminal v2.0

- Gerçek terminal emülasyonu
- Socket.IO ile gerçek zamanlı iletişim
- Otomatik yeniden bağlanma
- Durum göstergesi
- Görüntü desteği (sixel, iterm2 vb.)
- Komut geçmişi
- Renkli çıktı desteği
- Unicode11 desteği
- WebGL hızlandırma
- Bağlantılar için tıklanabilir URL'ler
- Terminal içeriğini kaydetme ve indirme
- Otomatik boyut ayarlama

### Terminal Yöneticisi

- Birden fazla terminal oturumu oluşturma
- Farklı çalışma dizinleri ile terminal başlatma
- Terminal oturumlarını yönetme
- Terminal sunucusunu başlatma/durdurma/yeniden başlatma
- Tam ekran modu
- Özelleştirilebilir terminal ayarları

## Teknik Detaylar

### Kullanılan Teknolojiler

- **Frontend**:
  - @xterm/xterm v5.5.0 (Terminal emülasyonu)
  - @xterm/addon-webgl (WebGL hızlandırma)
  - @xterm/addon-image (Görüntü desteği)
  - @xterm/addon-fit (Otomatik boyut ayarlama)
  - @xterm/addon-web-links (URL bağlantıları)
  - @xterm/addon-search (Terminal içinde arama)
  - @xterm/addon-serialize (Terminal içeriğini kaydetme)
  - @xterm/addon-unicode11 (Gelişmiş Unicode desteği)
  - socket.io-client (Socket.IO istemcisi)

- **Backend**:
  - express (HTTP sunucusu)
  - socket.io (WebSocket sunucusu)
  - node-pty (Terminal işlemleri)
  - cors (Cross-Origin Resource Sharing)

### Mimari

Terminal sistemi, istemci-sunucu mimarisi kullanır:

1. **Sunucu**: Node.js tabanlı bir sunucu, node-pty kullanarak gerçek terminal işlemlerini yönetir ve Socket.IO üzerinden istemcilerle iletişim kurar.

2. **İstemci**: Xterm.js tabanlı bir terminal emülatörü, Socket.IO üzerinden sunucuyla iletişim kurarak gerçek terminal deneyimi sağlar.

## Sorun Giderme

### Terminal Sunucusu Başlatılamıyor

Windows'ta node-pty modülünün derlenmesi için Visual Studio Build Tools gerekebilir. Aşağıdaki komutu çalıştırarak gerekli araçları yükleyebilirsiniz:

```
npm install --global --production windows-build-tools
```

### Socket.IO Bağlantı Hatası

Terminal sunucusunun çalıştığından ve 3001 portunu dinlediğinden emin olun. Firewall ayarlarınızı kontrol edin ve gerekirse 3001 portunu açın.

### WebGL Hatası

WebGL eklentisi yüklenemiyorsa, tarayıcınızın WebGL desteğini kontrol edin. Chrome veya Firefox'un en son sürümünü kullanmanız önerilir.

### Görüntü Desteği Çalışmıyor

Görüntü desteği için terminal emülatörünüzün ve işletim sisteminizin uyumlu olması gerekir. Windows Terminal, iTerm2 veya modern Linux terminal emülatörleri genellikle görüntü desteği sağlar.

### Diğer Sorunlar

Herhangi bir sorunla karşılaşırsanız, lütfen GitHub üzerinden bir issue açın veya terminal sunucusu loglarını kontrol edin.
